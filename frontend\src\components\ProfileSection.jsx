import React from 'react';
import { Link } from 'react-router-dom';

const ProfileSection = ({ settings = {}, loading = false }) => {
  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Image Skeleton */}
            <div className="order-2 lg:order-1">
              <div className="w-full h-96 bg-gray-300 rounded-lg animate-pulse"></div>
            </div>
            
            {/* Content Skeleton */}
            <div className="order-1 lg:order-2">
              <div className="h-8 bg-gray-300 rounded mb-4 animate-pulse"></div>
              <div className="space-y-3 mb-6">
                <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                <div className="h-4 bg-gray-300 rounded w-3/4 animate-pulse"></div>
              </div>
              <div className="h-10 bg-gray-300 rounded w-40 animate-pulse"></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* School Image */}
          <div className="order-2 lg:order-1">
            <div className="relative">
              {/* Main Image */}
              <div className="w-full h-96 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg overflow-hidden shadow-xl">
                {settings.schoolImage ? (
                  <img
                    src={settings.schoolImage}
                    alt={`Gedung ${settings.schoolName}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                ) : null}
                
                {/* Fallback Image */}
                <div 
                  className="w-full h-full flex items-center justify-center"
                  style={{ display: settings.schoolImage ? 'none' : 'flex' }}
                >
                  <div className="text-center">
                    <svg className="w-24 h-24 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    <p className="text-blue-600 font-semibold text-lg">
                      {settings.schoolShortName || 'Sekolah Kami'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-600 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-blue-400 rounded-full opacity-30 animate-pulse delay-1000"></div>
            </div>
          </div>

          {/* Content */}
          <div className="order-1 lg:order-2">
            <div className="max-w-xl">
              {/* Section Title */}
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Profil Kami
              </h2>

              {/* School Description */}
              <div className="prose prose-lg text-gray-600 mb-8">
                <p className="leading-relaxed mb-4">
                  {settings.schoolDescription || 
                    'Sekolah kami berkomitmen untuk memberikan pendidikan berkualitas tinggi dengan mengintegrasikan teknologi modern dan nilai-nilai karakter yang kuat.'
                  }
                </p>
                
                <p className="leading-relaxed mb-4">
                  Dengan fasilitas lengkap dan tenaga pengajar yang berpengalaman, kami menciptakan lingkungan belajar yang kondusif untuk mengembangkan potensi setiap siswa secara optimal.
                </p>

                <p className="leading-relaxed">
                  Visi kami adalah menjadi lembaga pendidikan terdepan yang menghasilkan generasi cerdas, berkarakter, dan siap menghadapi tantangan masa depan.
                </p>
              </div>

              {/* Key Features */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-600 rounded-full mr-3"></div>
                  <span className="text-gray-700 font-medium">Akreditasi A</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-600 rounded-full mr-3"></div>
                  <span className="text-gray-700 font-medium">Fasilitas Lengkap</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-600 rounded-full mr-3"></div>
                  <span className="text-gray-700 font-medium">Guru Berkualitas</span>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-600 rounded-full mr-3"></div>
                  <span className="text-gray-700 font-medium">Teknologi Modern</span>
                </div>
              </div>

              {/* CTA Button */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/about"
                  className="inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Baca Selengkapnya
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                
                <Link
                  to="/contact"
                  className="inline-flex items-center justify-center border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105"
                >
                  Hubungi Kami
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProfileSection;
