<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

try {
    echo "=== TESTING USER MODEL ===\n";
    
    // Test 1: Check if User model works
    echo "1. Testing User model...\n";
    $userCount = User::count();
    echo "   Total users: $userCount\n";
    
    // Test 2: Get first user
    echo "2. Testing first user...\n";
    $firstUser = User::first();
    if ($firstUser) {
        echo "   First user: {$firstUser->name} ({$firstUser->email})\n";
        echo "   Role: {$firstUser->role}\n";
        echo "   Active: " . ($firstUser->is_active ? 'Yes' : 'No') . "\n";
    } else {
        echo "   No users found!\n";
    }
    
    // Test 3: Test search scope
    echo "3. Testing search scope...\n";
    $searchResults = User::search('admin')->get();
    echo "   Search 'admin' results: " . $searchResults->count() . "\n";
    
    // Test 4: Test role scope
    echo "4. Testing role scope...\n";
    $adminUsers = User::role('admin')->get();
    echo "   Admin users: " . $adminUsers->count() . "\n";
    
    // Test 5: Test pagination
    echo "5. Testing pagination...\n";
    $paginatedUsers = User::paginate(10);
    echo "   Paginated users: " . $paginatedUsers->count() . " items\n";
    echo "   Total pages: " . $paginatedUsers->lastPage() . "\n";
    
    echo "\n=== ALL TESTS PASSED ===\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
}
