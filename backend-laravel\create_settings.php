<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Schema\Blueprint;

// Database configuration
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '127.0.0.1',
    'database' => 'school_management',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    echo "🔧 Creating school settings...\n";

    // Get admin user ID
    $adminId = $capsule->table('users')->where('role', 'admin')->value('id');
    
    if (!$adminId) {
        echo "❌ Admin user not found. Please create admin user first.\n";
        exit(1);
    }

    // School settings data
    $schoolSettings = [
        ['key' => 'schoolName', 'value' => '"SMA Negeri 1 Jakarta"', 'type' => 'string', 'category' => 'general', 'description' => 'Nama lengkap sekolah'],
        ['key' => 'schoolShortName', 'value' => '"SMAN 1 Jakarta"', 'type' => 'string', 'category' => 'general', 'description' => 'Nama singkat sekolah'],
        ['key' => 'schoolEmail', 'value' => '"<EMAIL>"', 'type' => 'string', 'category' => 'contact', 'description' => 'Email resmi sekolah'],
        ['key' => 'schoolPhone', 'value' => '"021-12345678"', 'type' => 'string', 'category' => 'contact', 'description' => 'Nomor telepon sekolah'],
        ['key' => 'schoolAddress', 'value' => '"Jl. Pendidikan No. 123, Menteng, Jakarta Pusat, DKI Jakarta 10310"', 'type' => 'string', 'category' => 'contact', 'description' => 'Alamat lengkap sekolah'],
        ['key' => 'schoolWebsite', 'value' => '"https://www.sman1jakarta.sch.id"', 'type' => 'string', 'category' => 'contact', 'description' => 'Website resmi sekolah'],
        ['key' => 'principalName', 'value' => '"Dr. Ahmad Suryadi, M.Pd"', 'type' => 'string', 'category' => 'general', 'description' => 'Nama kepala sekolah'],
        ['key' => 'schoolMotto', 'value' => '"Unggul dalam Prestasi, Berkarakter, dan Berwawasan Global"', 'type' => 'string', 'category' => 'general', 'description' => 'Motto sekolah'],
        ['key' => 'schoolDescription', 'value' => '"SMA Negeri 1 Jakarta adalah sekolah menengah atas negeri yang berkomitmen untuk memberikan pendidikan berkualitas tinggi dengan mengembangkan potensi akademik dan karakter siswa."', 'type' => 'string', 'category' => 'general', 'description' => 'Deskripsi sekolah'],
        ['key' => 'logoUrl', 'value' => '"/images/logo-school.png"', 'type' => 'string', 'category' => 'appearance', 'description' => 'URL logo sekolah'],
        ['key' => 'adminWhatsApp', 'value' => '"6281234567890"', 'type' => 'string', 'category' => 'contact', 'description' => 'Nomor WhatsApp admin untuk kontak']
    ];

    foreach ($schoolSettings as $setting) {
        // Check if setting exists
        $exists = $capsule->table('settings')->where('key', $setting['key'])->exists();
        
        if ($exists) {
            // Update existing setting
            $capsule->table('settings')
                ->where('key', $setting['key'])
                ->update([
                    'value' => $setting['value'],
                    'type' => $setting['type'],
                    'category' => $setting['category'],
                    'description' => $setting['description'],
                    'is_public' => true,
                    'updated_by' => $adminId,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            echo "  ✅ Updated {$setting['key']}\n";
        } else {
            // Create new setting
            $capsule->table('settings')->insert([
                'key' => $setting['key'],
                'value' => $setting['value'],
                'type' => $setting['type'],
                'category' => $setting['category'],
                'description' => $setting['description'],
                'is_public' => true,
                'updated_by' => $adminId,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            echo "  ✅ Created {$setting['key']}\n";
        }
    }

    echo "✅ School settings created/updated successfully!\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
