<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Laravel\Sanctum\PersonalAccessToken;

try {
    echo "=== CHECKING VALID TOKENS ===\n";
    
    // Get all tokens with user info
    $tokens = PersonalAccessToken::with('tokenable')->get();
    
    foreach ($tokens as $token) {
        echo "Token ID: {$token->id}\n";
        echo "Token Name: {$token->name}\n";
        echo "User: {$token->tokenable->name} ({$token->tokenable->email})\n";
        echo "Role: {$token->tokenable->role}\n";
        echo "Is Admin: " . ($token->tokenable->isAdmin() ? 'Yes' : 'No') . "\n";
        echo "Created: {$token->created_at}\n";
        echo "Last Used: " . ($token->last_used_at ?? 'Never') . "\n";
        
        // Show token format (first part)
        $tokenParts = explode('|', $token->getKey());
        if (count($tokenParts) >= 1) {
            echo "Token Format: {$tokenParts[0]}|[hash]\n";
        }
        
        echo "---\n";
    }
    
    echo "\n=== SOLUTION ===\n";
    echo "Frontend needs to login again to get a valid token.\n";
    echo "Or use existing admin credentials:\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
