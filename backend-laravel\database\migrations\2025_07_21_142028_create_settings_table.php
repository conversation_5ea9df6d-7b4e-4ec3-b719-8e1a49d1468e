<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->json('value');
            $table->enum('type', ['string', 'number', 'boolean', 'object', 'array'])->default('string');
            $table->text('description')->nullable();
            $table->enum('category', ['general', 'contact', 'social', 'appearance', 'system'])->default('general');
            $table->boolean('is_public')->default(true);

            // Gunakan cara ini jika tabel users mungkin belum ada saat migrasi ini dijalankan
            $table->unsignedBigInteger('updated_by');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('cascade');

            $table->timestamps();

            $table->index(['category', 'is_public']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
