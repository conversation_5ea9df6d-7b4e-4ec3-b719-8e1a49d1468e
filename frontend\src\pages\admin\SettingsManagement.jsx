import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import AdminLayout from '../../components/admin/AdminLayout';
import { Button, Card, Input, Textarea } from '../../components/ui';
import { FadeIn } from '../../components/ui/AnimatedComponents';
import { api } from '../../services/api';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import {
  validateLogoFile,
  createLogoPreview,
  getLogoUrl,
  DEFAULT_LOGO_PATH
} from '../../utils/logoUtils';

const DEFAULT_FAVICON_PATH = '/vite.svg';


// Validation schema
const settingsSchema = yup.object().shape({
  schoolName: yup
    .string()
    .required('Nama sekolah harus diisi')
    .min(5, '<PERSON>a sekolah minimal 5 karakter')
    .max(100, '<PERSON>a sekolah maksimal 100 karakter'),
  schoolShortName: yup
    .string()
    .required('<PERSON>a singkat sekolah harus diisi')
    .min(3, 'Nama singkat minimal 3 karakter')
    .max(20, 'Nama singkat maksimal 20 karakter'),
  schoolAddress: yup
    .string()
    .required('Alamat sekolah harus diisi')
    .min(10, 'Alamat minimal 10 karakter')
    .max(200, 'Alamat maksimal 200 karakter'),
  schoolPhone: yup
    .string()
    .required('Nomor telepon harus diisi')
    .matches(/^(\+62|62|0)[0-9]{8,13}$/, 'Format nomor telepon tidak valid'),
  schoolEmail: yup
    .string()
    .email('Format email tidak valid')
    .required('Email sekolah harus diisi'),
  schoolWebsite: yup
    .string()
    .url('Format website tidak valid')
    .required('Website sekolah harus diisi'),
  principalName: yup
    .string()
    .required('Nama kepala sekolah harus diisi')
    .min(3, 'Nama kepala sekolah minimal 3 karakter')
    .max(100, 'Nama kepala sekolah maksimal 100 karakter'),
  schoolMotto: yup
    .string()
    .required('Motto sekolah harus diisi')
    .min(5, 'Motto minimal 5 karakter')
    .max(200, 'Motto maksimal 200 karakter'),
  schoolDescription: yup
    .string()
    .required('Deskripsi sekolah harus diisi')
    .min(20, 'Deskripsi minimal 20 karakter')
    .max(1000, 'Deskripsi maksimal 1000 karakter')
});

const SettingsManagement = () => {
  const { isAuthenticated, isLoading: authLoading, authError, clearAuthError, redirectToLogin } = useAdminAuth();
  const [settings, setSettings] = useState({
    schoolName: '',
    schoolShortName: '',
    schoolAddress: '',
    schoolPhone: '',
    schoolEmail: '',
    schoolWebsite: '',
    principalName: '',
    schoolMotto: '',
    schoolDescription: '',
    logoUrl: ''
  });

  const [selectedLogo, setSelectedLogo] = useState({
    file: null,
    type: '',
    size: 0,
    name: ''
  });
  const [logoPreview, setLogoPreview] = useState(null);
  const [selectedFavicon, setSelectedFavicon] = useState({
    file: null,
    type: '',
    size: 0,
    name: '',
    preview: null
  });
  const [faviconPreview, setFaviconPreview] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [logoInfo, setLogoInfo] = useState({
    url: '',
    filename: '',
    file_size: 0,
    formatted_size: '0 KB',
    file_exists: false
  });
  const [faviconInfo, setFaviconInfo] = useState({
    url: '',
    filename: '',
    file_size: 0,
    formatted_size: '0 KB',
    file_exists: false
  });
  const [logoLoading, setLogoLoading] = useState(true);



  // React Hook Form
  const {
    register,
    handleSubmit: onSubmit,
    formState: { errors: formErrors, isSubmitting },
    reset
  } = useForm({
    resolver: yupResolver(settingsSchema),
    defaultValues: settings
  });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await api.get('/settings');

        if (response.data.success) {
          const settingsData = response.data.data;

          const finalSettings = {
            ...settingsData,
            logoUrl: settingsData.logoUrl || getLogoUrl()
          };

          setSettings(finalSettings);
          reset(finalSettings);
        } else {
          throw new Error('Failed to fetch settings');
        }
      } catch (err) {
        console.error('Error fetching settings:', err);
        setError('Terjadi kesalahan saat memuat pengaturan');

        // Fallback to localStorage
        const savedSettings = localStorage.getItem('schoolSettings');
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings);
          const settingsWithLogo = {
            ...parsedSettings,
            logoUrl: parsedSettings.logoUrl || getLogoUrl() // Use saved logo or default
          };
          setSettings(settingsWithLogo);
          reset(settingsWithLogo);
        } else {
          // If no saved settings, use default logo
          const defaultSettings = {
            schoolName: '',
            schoolShortName: '',
            schoolAddress: '',
            schoolPhone: '',
            schoolEmail: '',
            schoolWebsite: '',
            principalName: '',
            schoolMotto: '',
            schoolDescription: '',
            logoUrl: getLogoUrl()
          };
          setSettings(defaultSettings);
          reset(defaultSettings);
        }
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [reset]);

  // Load latest logo info and all logos
  useEffect(() => {
    const loadLogoInfo = async () => {
      setLogoLoading(true);

      try {
        // Get current logo from API
        const response = await api.get('/logo/current');
        if (response.data.success) {
          const logoData = response.data.data;
          setLogoInfo(logoData);

          // Update settings dengan logo dari server
          setSettings(prev => ({
            ...prev,
            logoUrl: logoData.url
          }));
        }
      } catch {
        // Jika tidak ada logo, gunakan default
        setSettings(prev => ({
          ...prev,
          logoUrl: DEFAULT_LOGO_PATH
        }));
        setLogoInfo(null);
      } finally {
        setLogoLoading(false);
      }
    };

    loadLogoInfo();

    // Listen for logo updates
    const handleLogoUpdate = () => {
      loadLogoInfo();
    };

    window.addEventListener('logoUpdated', handleLogoUpdate);

    return () => {
      window.removeEventListener('logoUpdated', handleLogoUpdate);
    };
  }, []);

  const handleFormSubmit = async (data) => {
    setIsSaving(true);
    try {
      const response = await api.put('/settings', {
        ...data,
        logoUrl: settings.logoUrl
      });

      const result = response.data;

      if (result.success) {
        const updatedSettings = result.data;
        setSettings(updatedSettings);

        // Update localStorage as cache
        localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));

        // Trigger custom event to update other components
        window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', {
          detail: updatedSettings
        }));

        // Show success message
        setError(null);
        setShowSuccessModal(true);
        setTimeout(() => setShowSuccessModal(false), 3000);
      } else {
        throw new Error(result.error || 'Failed to save settings');
      }
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Terjadi kesalahan saat menyimpan pengaturan: ' + err.message);
    } finally {
      setIsSaving(false);
    }
  };


  // Logo file handler
  const handleLogoChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const validation = validateLogoFile(file);
      if (!validation.success) {
        setError(validation.message);
        return;
      }
      setSelectedLogo({
        file,
        type: file.type,
        size: file.size,
        name: file.name
      });
      try {
        const preview = await createLogoPreview(file);
        setLogoPreview(preview);
      } catch (error) {
        console.error('Error creating preview:', error);
        setError('Gagal membuat preview gambar');
      }
    }
  };

  // Favicon file handler
  const handleFaviconChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        setError('Favicon harus berformat .ico atau .png');
        return;
      }
      // Clean up previous preview URL if exists
      if (selectedFavicon.preview) {
        URL.revokeObjectURL(selectedFavicon.preview);
      }
      const previewUrl = URL.createObjectURL(file);
      setSelectedFavicon({
        file,
        type: file.type,
        size: file.size,
        name: file.name,
        preview: previewUrl
      });
      setFaviconPreview(previewUrl);
      setError(null);
    }
  };

  const handleLogoSave = async () => {
    const hasLogoToUpload = selectedLogo && selectedLogo.file && logoPreview;
    const hasFaviconToUpload = selectedFavicon && selectedFavicon.file && faviconPreview;

    if (!hasLogoToUpload && !hasFaviconToUpload) {
      setError('Silakan pilih logo atau favicon terlebih dahulu');
      return;
    }

    setIsSaving(true);
    try {
      // Upload logo if available
      if (hasLogoToUpload) {
        const formData = new FormData();
        formData.append('logo', selectedLogo.file);
        formData.append('description', 'Logo sekolah diupload dari admin panel');
        const uploadUrl = 'http://localhost:8000/api/logo/upload';
        const response = await fetch(uploadUrl, {
          method: 'POST',
          body: formData
        });
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }
        const result = await response.json();
        if (result.success) {
          const logoData = result.data;
          const updatedSettings = {
            ...settings,
            logoUrl: logoData.url
          };
          setSettings(updatedSettings);
          localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));
          try {
            await api.put('/settings', {
              ...updatedSettings,
              logoUrl: logoData.url
            });
          } catch {}
          window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', {
            detail: updatedSettings
          }));
          window.dispatchEvent(new CustomEvent('logoUpdated'));
          setSelectedLogo({ file: null, type: '', size: 0, name: '' });
          setLogoPreview(null);
          setLogoInfo({
            url: logoData.url,
            filename: logoData.filename,
            file_size: logoData.file_size,
            formatted_size: logoData.formatted_size,
            file_exists: true
          });
          setShowSuccessModal(true);
          setTimeout(() => {
            setShowSuccessModal(false);
            window.location.reload();
          }, 2000);
        } else {
          throw new Error(result.message || 'Failed to upload logo');
        }
      }
      // Upload favicon if available
      if (hasFaviconToUpload) {
        const formData = new FormData();
        formData.append('favicon', selectedFavicon.file);
        formData.append('description', 'Favicon diupload dari admin panel');
        const uploadUrl = 'http://localhost:8000/api/favicon/upload';
        const response = await fetch(uploadUrl, {
          method: 'POST',
          body: formData
        });
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }
        const result = await response.json();
        if (result.success) {
          setFaviconInfo({
            url: result.data.url,
            filename: result.data.filename,
            file_size: result.data.file_size,
            formatted_size: result.data.formatted_size,
            file_exists: true
          });
          setSelectedFavicon({ file: null, type: '', size: 0, name: '', preview: null });
          setFaviconPreview(null);
          setShowSuccessModal(true);
          setTimeout(() => {
            setShowSuccessModal(false);
            window.location.reload();
          }, 2000);
        } else {
          throw new Error(result.message || 'Failed to upload favicon');
        }
      }
    } catch (err) {
      console.error('❌ Error uploading logo/favicon:', err);
      let errorMessage = 'Terjadi kesalahan saat mengupload logo/favicon';
      if (!err.response) {
        errorMessage = 'Tidak dapat terhubung ke server. Pastikan backend berjalan di http://localhost:8000';
      } else {
        const status = err.response.status;
        const data = err.response.data;
        if (status === 500) {
          errorMessage = 'Server error (500). Periksa log server backend.';
        } else if (status === 422) {
          errorMessage = 'Validasi gagal. Periksa format dan ukuran file.';
        } else if (status === 401) {
          errorMessage = 'Tidak terautentikasi. Silakan login kembali.';
        } else if (status === 403) {
          errorMessage = 'Tidak memiliki akses. Pastikan Anda login sebagai admin.';
        } else if (data?.message) {
          errorMessage = data.message;
        } else {
          errorMessage = `HTTP Error ${status}`;
        }
      }
      setError(`Upload gagal: ${errorMessage}`);
    } finally {
      setIsSaving(false);
    }
  };



  // Test upload with sample file
  const handleTestUpload = async () => {
    try {
      // Create a simple test image (1x1 pixel PNG)
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#3B82F6';
      ctx.fillRect(0, 0, 1, 1);

      canvas.toBlob(async (blob) => {
        const testFile = new File([blob], 'test-logo.png', { type: 'image/png' });

        console.log('🧪 Testing upload with generated file...');

        const formData = new FormData();
        formData.append('logo', testFile);
        formData.append('description', 'Test upload from frontend');

        const response = await api.post('/logos/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (response.data.success) {
          const logoData = response.data.data;
          console.log('✅ Test upload successful:', logoData);

          // Update UI with test logo
          const updatedSettings = {
            ...settings,
            logoUrl: logoData.url
          };
          setSettings(updatedSettings);

          // Refresh logo info
          setLogoInfo({
            url: logoData.url,
            filename: logoData.filename,
            file_size: logoData.file_size,
            formatted_size: logoData.formatted_size,
            file_exists: true
          });

          // Test upload berhasil - tidak perlu alert
        }
      }, 'image/png');

    } catch (error) {
      console.error('❌ Test upload error:', error);
      setError(`Test upload gagal: ${error.message}`);
    }
  };

  const handleLogoReset = async () => {
    try {
      // For database version, we'll just set the default logo URL
      const defaultLogo = DEFAULT_LOGO_PATH;
      const updatedSettings = {
        ...settings,
        logoUrl: defaultLogo
      };

      setSettings(updatedSettings);
      localStorage.setItem('schoolSettings', JSON.stringify(updatedSettings));

      // Clear logo info
      setLogoInfo(null);

      // Clear cache
      sessionStorage.removeItem('cachedLogoUrl');

      // Trigger events
      window.dispatchEvent(new CustomEvent('schoolSettingsUpdated', {
        detail: updatedSettings
      }));

      window.dispatchEvent(new CustomEvent('logoUpdated'));

      // Logo berhasil direset - tidak perlu alert
    } catch (error) {
      console.error('Error resetting logo:', error);
      setError('Terjadi kesalahan saat mereset logo');
    }
  };

  // Show auth loading
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show auth error if not authenticated
  if (!isAuthenticated) {
    return <AuthErrorComponent authError={authError} clearAuthError={clearAuthError} redirectToLogin={redirectToLogin} />;
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="flex items-center justify-center min-h-96">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Memuat pengaturan...</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            <h3 className="font-semibold">Error</h3>
            <p>{error}</p>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Muat Ulang
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        {/* Header */}
        <FadeIn direction="down" className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Pengaturan Sekolah</h1>
          <p className="text-gray-600 mt-2">Kelola informasi dan pengaturan umum sekolah</p>
        </FadeIn>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Logo Settings */}
          <FadeIn delay={0.2} className="lg:col-span-1">
            <Card padding="lg">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Logo Sekolah</h2>
              
              <div className="text-center">
                <div className="mb-6">
                  {logoLoading ? (
                    // Loading state
                    <div className="w-32 h-32 mx-auto bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                        <p className="text-xs text-gray-500">Memuat logo...</p>
                      </div>
                    </div>
                  ) : logoPreview || settings.logoUrl ? (
                    <div className="relative">
                      <img
                        src={logoPreview || settings.logoUrl}
                        alt="Logo Sekolah"
                        className="w-32 h-32 mx-auto object-contain bg-gray-50 rounded-lg border-2 border-gray-200 shadow-sm transition-opacity duration-300"
                        onError={(e) => {
                          e.target.src = DEFAULT_LOGO_PATH;
                        }}
                        onLoad={(e) => {
                          e.target.style.opacity = '1';
                        }}
                        style={{
                          minHeight: '128px',
                          minWidth: '128px',
                          opacity: logoLoading ? '0' : '1'
                        }}
                      />
                      {logoPreview && (
                        <div className="absolute -top-2 -right-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Preview
                          </span>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="w-32 h-32 mx-auto bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                      <div className="text-center">
                        {/* Placeholder Logo Icon */}
                        <svg className="w-16 h-16 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 128 128">
                          {/* Background Circle */}
                          <circle cx="64" cy="64" r="56" fill="#F3F4F6" stroke="#D1D5DB" strokeWidth="2"/>

                          {/* Simple School Building */}
                          <g transform="translate(32, 32)">
                            {/* Main Building */}
                            <rect x="20" y="32" width="24" height="24" fill="none" stroke="#9CA3AF" strokeWidth="1.5"/>

                            {/* Roof */}
                            <path d="M16 32 L32 20 L48 32" fill="none" stroke="#9CA3AF" strokeWidth="1.5"/>

                            {/* Door */}
                            <rect x="28" y="44" width="8" height="12" fill="none" stroke="#9CA3AF" strokeWidth="1"/>

                            {/* Windows */}
                            <rect x="22" y="36" width="4" height="4" fill="none" stroke="#9CA3AF" strokeWidth="1"/>
                            <rect x="38" y="36" width="4" height="4" fill="none" stroke="#9CA3AF" strokeWidth="1"/>
                          </g>
                        </svg>
                        <p className="text-xs text-gray-500 font-medium">Belum ada logo</p>
                        <p className="text-xs text-gray-400 mt-1">Upload logo sekolah</p>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="space-y-3">
                  <div className="relative">
                    <input
                      id="logo-upload"
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp"
                      onChange={handleLogoChange}
                      className="hidden"
                    />
                    <Button
                      variant="primary"
                      size="sm"
                      className="cursor-pointer"
                      onClick={() => document.getElementById('logo-upload').click()}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                      }
                    >
                      {logoPreview ? 'Ganti Logo' : 'Upload Logo Baru'}
                    </Button>
                  </div>
                  
                  {logoPreview && (
                    <div className="flex space-x-2">
                      <Button
                        variant="success"
                        size="sm"
                        onClick={handleLogoSave}
                        loading={isSaving}
                        disabled={isSaving}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        }
                      >
                        Simpan Logo
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleTestUpload}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        }
                      >
                        Test Upload
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setLogoPreview(null);
                          setSelectedLogo(null);
                        }}
                        disabled={isSaving}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        }
                      >
                        Batal
                      </Button>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleLogoReset}
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    }
                  >
                    Reset ke Default
                  </Button>


                </div>
                
                <p className="text-xs text-gray-500 mt-4">
                  Format: JPG, PNG, WebP<br />
                  Ukuran maksimal: 5MB<br />
                  Rekomendasi: 512x512px
                </p>

                {/* Logo Information */}
                {logoInfo && logoInfo.filename && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="text-xs font-semibold text-blue-800 mb-2">Logo Terbaru:</h4>
                    <div className="text-xs text-blue-700 space-y-1">
                      <div><strong>File:</strong> {logoInfo.filename}</div>
                      <div><strong>Ukuran:</strong> {logoInfo.formatted_size || '0 KB'}</div>
                      <div><strong>Status:</strong> {logoInfo.file_exists ? '✅ File ada' : '❌ File tidak ditemukan'}</div>
                    </div>
                  </div>
                )}

                {/* Logo Info */}
                {logoInfo && (
                  <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="text-xs text-green-800">
                      <div className="font-medium">Logo Aktif:</div>
                      <div>{logoInfo.filename || 'logo-school.png'}</div>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </FadeIn>

          {/* School Information Form */}
          <FadeIn delay={0.4} className="lg:col-span-2">
            <Card padding="lg">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Informasi Sekolah</h2>
              
              <form onSubmit={onSubmit(handleFormSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Nama Sekolah Lengkap"
                    placeholder="SMA Negeri 1 Jakarta"
                    autoComplete="organization"
                    required
                    error={formErrors.schoolName?.message}
                    {...register('schoolName')}
                  />

                  <Input
                    label="Nama Singkat"
                    placeholder="SMAN 1 Jakarta"
                    autoComplete="off"
                    required
                    error={formErrors.schoolShortName?.message}
                    {...register('schoolShortName')}
                  />
                </div>

                <Textarea
                  label="Alamat Sekolah"
                  placeholder="Jl. Pendidikan No. 123, Menteng, Jakarta Pusat"
                  rows={3}
                  autoComplete="street-address"
                  required
                  error={formErrors.schoolAddress?.message}
                  {...register('schoolAddress')}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Nomor Telepon"
                    placeholder="021-12345678"
                    type="tel"
                    autoComplete="tel"
                    required
                    error={formErrors.schoolPhone?.message}
                    {...register('schoolPhone')}
                  />

                  <Input
                    label="Email Sekolah"
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    required
                    error={formErrors.schoolEmail?.message}
                    {...register('schoolEmail')}
                  />
                </div>

                <Input
                  label="Website Sekolah"
                  type="url"
                  placeholder="https://www.sekolah.sch.id"
                  autoComplete="url"
                  required
                  error={formErrors.schoolWebsite?.message}
                  {...register('schoolWebsite')}
                />

                <Input
                  label="Nama Kepala Sekolah"
                  placeholder="Dr. Ahmad Suryadi, M.Pd"
                  autoComplete="name"
                  required
                  error={formErrors.principalName?.message}
                  {...register('principalName')}
                />

                <Input
                  label="Motto Sekolah"
                  placeholder="Unggul dalam Prestasi, Berkarakter, dan Berwawasan Global"
                  autoComplete="off"
                  required
                  error={formErrors.schoolMotto?.message}
                  {...register('schoolMotto')}
                />

                <Textarea
                  label="Deskripsi Sekolah"
                  placeholder="Deskripsi singkat tentang sekolah..."
                  rows={4}
                  autoComplete="off"
                  required
                  error={formErrors.schoolDescription?.message}
                  {...register('schoolDescription')}
                />

                <div className="flex justify-end pt-4">
                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    loading={isSubmitting || isSaving}
                    disabled={isSubmitting || isSaving}
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    }
                  >
                    Simpan Pengaturan
                  </Button>
                </div>
              </form>
            </Card>
          </FadeIn>
        </div>

        {/* Carousel & News Settings */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
          {/* Carousel Settings */}
          <FadeIn delay={0.6}>
            <Card padding="lg">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Pengaturan Carousel</h2>

              <div className="space-y-6">
                {/* Pin to Carousel Section */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Pin Gambar ke Carousel</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Pilih gambar dari galeri untuk ditampilkan di carousel utama halaman beranda
                  </p>

                  <div className="bg-gray-50 rounded-lg p-4 border-2 border-dashed border-gray-300">
                    <div className="text-center">
                      <svg className="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p className="text-gray-600 mb-3">Kelola gambar carousel</p>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={() => {
                          // Navigate to gallery management
                          window.location.href = '/admin/gallery';
                        }}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2v12a2 2 0 002 2z" />
                          </svg>
                        }
                      >
                        Kelola Gambar Gallery
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Carousel Settings */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Pengaturan Tampilan</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Tinggi Carousel
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="h-[500px]">Normal (500px)</option>
                        <option value="h-[600px]" selected>Besar (600px)</option>
                        <option value="h-[700px]">Sangat Besar (700px)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Durasi Auto-play (detik)
                      </label>
                      <input
                        type="number"
                        min="3"
                        max="10"
                        defaultValue="5"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="showDate"
                        defaultChecked
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="showDate" className="ml-2 block text-sm text-gray-700">
                        Tampilkan tanggal upload
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="darkOverlay"
                        defaultChecked
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="darkOverlay" className="ml-2 block text-sm text-gray-700">
                        Overlay gelap untuk teks
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </FadeIn>

          {/* News Settings */}
          <FadeIn delay={0.8}>
            <Card padding="lg">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Pengaturan Berita</h2>

              <div className="space-y-6">
                {/* Background Settings */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Background Section Berita</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Warna Background
                      </label>
                      <div className="flex items-center space-x-3">
                        <input
                          type="color"
                          defaultValue="#1f2937"
                          className="h-10 w-16 border border-gray-300 rounded cursor-pointer"
                        />
                        <input
                          type="text"
                          defaultValue="#1f2937"
                          placeholder="#1f2937"
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Warna background gelap untuk section berita</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Background Image (Opsional)
                      </label>
                      <div className="space-y-3">
                        <input
                          type="file"
                          accept="image/*"
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <div className="bg-gray-50 rounded-lg p-3 border">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-600">Tidak ada gambar background</span>
                            <Button variant="outline" size="sm">
                              Upload
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Opacity Overlay
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        defaultValue="70"
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>Transparan</span>
                        <span>70%</span>
                        <span>Gelap</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* News Display Settings */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Pengaturan Tampilan</h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Jumlah Berita per Halaman
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="6">6 Berita (2x3)</option>
                        <option value="9" selected>9 Berita (3x3)</option>
                        <option value="12">12 Berita (4x3)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Panjang Excerpt (karakter)
                      </label>
                      <input
                        type="number"
                        min="50"
                        max="200"
                        defaultValue="120"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="showViews"
                        defaultChecked
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="showViews" className="ml-2 block text-sm text-gray-700">
                        Tampilkan jumlah views
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="showCategory"
                        defaultChecked
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="showCategory" className="ml-2 block text-sm text-gray-700">
                        Tampilkan kategori berita
                      </label>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="pt-4 border-t border-gray-200">
                  <div className="flex space-x-3">
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={() => {
                        window.location.href = '/admin/news';
                      }}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      }
                    >
                      Kelola Berita
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Save news settings
                        alert('Pengaturan berita disimpan!');
                      }}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      }
                    >
                      Simpan Pengaturan
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </FadeIn>
        </div>

        {/* Success Modal */}
        {showSuccessModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Berhasil!</h3>
              <p className="text-gray-600 mb-4">Logo berhasil diupload dan disimpan</p>
              <div className="text-sm text-gray-500">Halaman akan dimuat ulang...</div>
            </div>
          </div>
        )}

      </div>
    </AdminLayout>
  );
};

export default SettingsManagement;
