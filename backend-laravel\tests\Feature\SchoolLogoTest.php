<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;
use App\Models\User;

class SchoolLogoTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create storage disk for testing
        Storage::fake('public');
        
        // Create admin user for testing
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'admin'
        ]);
    }

    /** @test */
    public function it_can_upload_logo_successfully()
    {
        // Create a fake image file
        $file = UploadedFile::fake()->image('test-logo.jpg', 500, 500)->size(1000); // 1MB

        // Act as authenticated admin
        $response = $this->actingAs($this->adminUser, 'sanctum')
            ->postJson('/api/logos/upload', [
                'logo' => $file,
                'description' => 'Test logo upload'
            ]);

        // Assert response
        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Logo uploaded successfully'
                ]);

        // Assert database
        $this->assertDatabaseHas('school_logos', [
            'original_name' => 'test-logo.jpg',
            'is_active' => 1,
            'description' => 'Test logo upload'
        ]);

        // Assert file was stored
        $logoData = $response->json('data');
        Storage::disk('public')->assertExists($logoData['filename']);
    }

    /** @test */
    public function it_validates_file_type_on_upload()
    {
        // Create a fake non-image file
        $file = UploadedFile::fake()->create('document.pdf', 1000);

        $response = $this->actingAs($this->adminUser, 'sanctum')
            ->postJson('/api/logos/upload', [
                'logo' => $file
            ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function it_validates_file_size_on_upload()
    {
        // Create a fake large image file (3MB)
        $file = UploadedFile::fake()->image('large-logo.jpg', 1000, 1000)->size(3000);

        $response = $this->actingAs($this->adminUser, 'sanctum')
            ->postJson('/api/logos/upload', [
                'logo' => $file
            ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function it_deactivates_previous_logos_when_uploading_new_one()
    {
        // Insert existing logo
        DB::table('school_logos')->insert([
            'filename' => 'old_logo.jpg',
            'original_name' => 'old_logo.jpg',
            'file_path' => 'logos/old_logo.jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1000,
            'is_active' => 1,
            'description' => 'Old logo',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Upload new logo
        $file = UploadedFile::fake()->image('new-logo.jpg', 500, 500)->size(1000);

        $response = $this->actingAs($this->adminUser, 'sanctum')
            ->postJson('/api/logos/upload', [
                'logo' => $file,
                'description' => 'New logo'
            ]);

        $response->assertStatus(200);

        // Assert old logo is deactivated
        $this->assertDatabaseHas('school_logos', [
            'filename' => 'old_logo.jpg',
            'is_active' => 0
        ]);

        // Assert new logo is active
        $this->assertDatabaseHas('school_logos', [
            'original_name' => 'new-logo.jpg',
            'is_active' => 1
        ]);
    }

    /** @test */
    public function it_can_get_current_active_logo()
    {
        // Insert test logo
        DB::table('school_logos')->insert([
            'filename' => 'current_logo.jpg',
            'original_name' => 'current_logo.jpg',
            'file_path' => 'logos/current_logo.jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1000,
            'is_active' => 1,
            'description' => 'Current logo',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $response = $this->getJson('/api/logos/current');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'filename' => 'current_logo.jpg',
                        'is_active' => 1
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_latest_logo()
    {
        // Insert multiple logos
        DB::table('school_logos')->insert([
            [
                'filename' => 'logo1.jpg',
                'original_name' => 'logo1.jpg',
                'file_path' => 'logos/logo1.jpg',
                'mime_type' => 'image/jpeg',
                'file_size' => 1000,
                'is_active' => 0,
                'description' => 'Logo 1',
                'created_at' => now()->subHour(),
                'updated_at' => now()->subHour()
            ],
            [
                'filename' => 'logo2.jpg',
                'original_name' => 'logo2.jpg',
                'file_path' => 'logos/logo2.jpg',
                'mime_type' => 'image/jpeg',
                'file_size' => 1000,
                'is_active' => 1,
                'description' => 'Logo 2',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);

        $response = $this->getJson('/api/logos/latest');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'filename' => 'logo2.jpg'
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_all_logos()
    {
        // Insert test logos
        DB::table('school_logos')->insert([
            [
                'filename' => 'logo1.jpg',
                'original_name' => 'logo1.jpg',
                'file_path' => 'logos/logo1.jpg',
                'mime_type' => 'image/jpeg',
                'file_size' => 1000,
                'is_active' => 0,
                'description' => 'Logo 1',
                'created_at' => now()->subHour(),
                'updated_at' => now()->subHour()
            ],
            [
                'filename' => 'logo2.jpg',
                'original_name' => 'logo2.jpg',
                'file_path' => 'logos/logo2.jpg',
                'mime_type' => 'image/jpeg',
                'file_size' => 1000,
                'is_active' => 1,
                'description' => 'Logo 2',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);

        $response = $this->getJson('/api/logos/all');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true
                ])
                ->assertJsonCount(2, 'data');
    }

    /** @test */
    public function it_requires_authentication_for_upload()
    {
        $file = UploadedFile::fake()->image('test-logo.jpg', 500, 500);

        $response = $this->postJson('/api/logos/upload', [
            'logo' => $file
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function it_returns_proper_error_when_no_logo_exists()
    {
        $response = $this->getJson('/api/logos/current');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => false,
                    'message' => 'No active logo found'
                ]);
    }
}
