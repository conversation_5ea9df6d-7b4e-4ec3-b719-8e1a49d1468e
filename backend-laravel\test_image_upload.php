<?php

// Test image upload path
$uploadPath = __DIR__ . '/../frontend/public/images/news';

echo "Testing image upload path...\n";
echo "Upload path: $uploadPath\n";

// Check if path exists
if (file_exists($uploadPath)) {
    echo "✅ Path exists\n";
} else {
    echo "❌ Path does not exist, creating...\n";
    if (mkdir($uploadPath, 0755, true)) {
        echo "✅ Path created successfully\n";
    } else {
        echo "❌ Failed to create path\n";
    }
}

// Check if path is writable
if (is_writable($uploadPath)) {
    echo "✅ Path is writable\n";
} else {
    echo "❌ Path is not writable\n";
}

// Test creating a file
$testFile = $uploadPath . '/test.txt';
if (file_put_contents($testFile, 'test')) {
    echo "✅ Can write files to path\n";
    unlink($testFile); // Clean up
} else {
    echo "❌ Cannot write files to path\n";
}

echo "\nDone!\n";
