import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const News = () => {
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const navigate = useNavigate();

  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';
  const categories = ['all', 'Prestasi', 'Akademik', 'Kegiatan', 'Pengumuman', 'Fasilitas'];

  // Fetch news data from API
  useEffect(() => {
    const fetchNews = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`${API_BASE_URL}/news`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          setNews(data.data || []);
        } else {
          throw new Error(data.message || 'Failed to fetch news');
        }
      } catch (err) {
        console.error('Error fetching news:', err);
        setError(err.message);
        setNews([]);
      } finally {
        setLoading(false);
      }
    };

    fetchNews();
  }, []);

  // Filter news berdasarkan kategori
  const filteredNews = news.filter(item => {
    return selectedCategory === 'all' || item.category === selectedCategory;
  });

  // Handle click to detail page
  const handleNewsClick = (newsId) => {
    navigate(`/news/${newsId}`);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-gray-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          <h1 className="text-3xl font-bold text-gray-800 text-center">
            Berita & Artikel
          </h1>
        </div>
      </div>

      {/* Category Filter */}
      <div className="max-w-6xl mx-auto px-4 py-4">
        <div className="flex flex-wrap justify-center gap-2 mb-6">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                selectedCategory === category
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category === 'all' ? 'Semua' : category}
            </button>
          ))}
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Memuat berita...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
            <p className="text-red-600">Error: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Coba Lagi
            </button>
          </div>
        </div>
      )}

      {/* News Grid */}
      {!loading && !error && (
        <div className="max-w-6xl mx-auto px-4 py-8">
          {filteredNews.length > 0 ? (
            <div className="grid grid-cols-1 min-[900px]:grid-cols-3 gap-6">
              {filteredNews.map((newsItem) => (
                <div
                  key={newsItem.id}
                  onClick={() => handleNewsClick(newsItem.id)}
                  className="bg-white border-2 border-gray-300 rounded-lg shadow-sm hover:shadow-md hover:border-blue-400 transition-all duration-200 cursor-pointer"
                >
                  {/* Image */}
                  <div className="aspect-[3/2] overflow-hidden rounded-t-lg">
                    <img
                      src={newsItem.image_url || '/placeholder-image.jpg'}
                      alt={newsItem.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.src = '/placeholder-image.jpg';
                      }}
                    />
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-2 line-clamp-2">
                      {newsItem.title}
                    </h3>
                    <div className="flex justify-between items-center mb-2">
                      <p className="text-sm text-gray-500">
                        {newsItem.category}
                      </p>
                      {newsItem.featured && (
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                          Unggulan
                        </span>
                      )}
                    </div>
                    {newsItem.excerpt && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-3">
                        {newsItem.excerpt}
                      </p>
                    )}
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span>{newsItem.author?.name || 'Admin'}</span>
                      <span>{new Date(newsItem.published_at).toLocaleDateString('id-ID')}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Belum Ada Berita
              </h3>
              <p className="text-gray-500">
                {selectedCategory === 'all'
                  ? 'Belum ada berita yang tersedia saat ini.'
                  : `Belum ada berita untuk kategori "${selectedCategory}".`
                }
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default News;
