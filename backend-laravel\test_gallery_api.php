<?php

/**
 * Test script untuk memeriksa API gallery dan data kategori
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Http\Controllers\GalleryController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING GALLERY API & DATA ===\n\n";

// Test 1: Check database data
echo "1. Current gallery data in database:\n";
$galleries = DB::table('galleries')->select('id', 'title', 'category', 'image_url')->get();

foreach ($galleries as $gallery) {
    echo "ID: {$gallery->id} | Title: {$gallery->title} | Category: {$gallery->category}\n";
}

echo "\n";

// Test 2: Test adminIndex API
echo "2. Testing adminIndex API (what frontend receives):\n";
try {
    $controller = new GalleryController();
    $request = new Request();
    $response = $controller->adminIndex($request);
    $responseData = json_decode($response->getContent(), true);
    
    if ($responseData['success']) {
        echo "✅ API Response successful\n";
        echo "📊 Total items: " . count($responseData['data']) . "\n\n";
        
        foreach ($responseData['data'] as $item) {
            echo "API Data - ID: {$item['id']} | Title: {$item['title']} | Category: {$item['category']}\n";
        }
    } else {
        echo "❌ API Response failed: " . $responseData['message'] . "\n";
    }
} catch (Exception $e) {
    echo "❌ API Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Check valid categories
echo "3. Valid categories check:\n";
$validCategories = ['Fasilitas', 'Kegiatan', 'Prestasi', 'Ekstrakurikuler', 'Umum'];
echo "Valid categories: " . implode(', ', $validCategories) . "\n\n";

// Test 4: Check for invalid categories and fix them
echo "4. Checking and fixing invalid categories:\n";
$invalidCount = 0;

foreach ($galleries as $gallery) {
    if (!in_array($gallery->category, $validCategories)) {
        $invalidCount++;
        echo "❌ Invalid category found - ID: {$gallery->id}, Category: '{$gallery->category}'\n";
        
        // Auto-fix common invalid categories
        $categoryMapping = [
            'Kegiatan Rutin' => 'Kegiatan',
            'Kegiatan Khusus' => 'Kegiatan',
            'Prestasi Siswa' => 'Prestasi',
            'Fasilitas Sekolah' => 'Fasilitas',
            'Ekstrakurikuler Sekolah' => 'Ekstrakurikuler',
            'Lainnya' => 'Umum',
            'Other' => 'Umum',
            'General' => 'Umum'
        ];
        
        $newCategory = $categoryMapping[$gallery->category] ?? 'Umum';
        
        try {
            DB::table('galleries')
                ->where('id', $gallery->id)
                ->update(['category' => $newCategory]);
            
            echo "✅ Fixed ID {$gallery->id}: '{$gallery->category}' → '{$newCategory}'\n";
        } catch (Exception $e) {
            echo "❌ Error fixing ID {$gallery->id}: " . $e->getMessage() . "\n";
        }
    }
}

if ($invalidCount === 0) {
    echo "✅ All categories are valid!\n";
}

echo "\n";

// Test 5: Final verification
echo "5. Final verification after fixes:\n";
$updatedGalleries = DB::table('galleries')->select('id', 'title', 'category')->get();

foreach ($updatedGalleries as $gallery) {
    $isValid = in_array($gallery->category, $validCategories);
    $status = $isValid ? '✅' : '❌';
    echo "{$status} ID: {$gallery->id} | Title: {$gallery->title} | Category: {$gallery->category}\n";
}

echo "\n";

// Test 6: Test API again after fixes
echo "6. Testing API again after fixes:\n";
try {
    $controller = new GalleryController();
    $request = new Request();
    $response = $controller->adminIndex($request);
    $responseData = json_decode($response->getContent(), true);
    
    if ($responseData['success']) {
        echo "✅ API Response successful after fixes\n";
        echo "📊 Total items: " . count($responseData['data']) . "\n\n";
        
        echo "Categories in API response:\n";
        foreach ($responseData['data'] as $item) {
            $isValid = in_array($item['category'], $validCategories);
            $status = $isValid ? '✅' : '❌';
            echo "{$status} ID: {$item['id']} | Category: {$item['category']}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ API Error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
echo "Now the frontend edit modal should show correct categories!\n\n";
