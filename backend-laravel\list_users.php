<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "=== AVAILABLE USERS FOR LOGIN ===\n\n";

$users = User::where('is_active', true)->get(['id', 'name', 'email', 'role']);

foreach ($users as $user) {
    echo "ID: " . $user->id . "\n";
    echo "Name: " . $user->name . "\n";
    echo "Email: " . $user->email . "\n";
    echo "Role: " . $user->role . "\n";
    echo "Password: password123 (for testing)\n";
    echo "---\n";
}

echo "\nTotal active users: " . $users->count() . "\n";
echo "\n=== LOGIN CREDENTIALS ===\n";
echo "Main Admin: <EMAIL> / admin123\n";
echo "Other users: [email above] / password123\n";
