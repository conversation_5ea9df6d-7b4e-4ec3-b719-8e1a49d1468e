<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing users except admin
        User::where('email', '!=', '<EMAIL>')->delete();

        // Create sample users
        $users = [
            [
                'name' => 'Dr. Siti N<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'kepala_sekolah',
                'phone' => '081234567890',
                'bio' => 'Kepala Sekolah SMAN 1 Jakarta dengan pengalaman 15 tahun di bidang pendidikan.',
                'position' => 'Kepala Sekolah',
                'employee_id' => '196801011990032001',
                'is_active' => true,
                'last_login' => now()->subHours(2),
            ],
            [
                'name' => '<PERSON>, S.<PERSON>, M.Pd',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'guru',
                'phone' => '081234567891',
                'bio' => 'Guru Matematika dengan spesialisasi Aljabar dan Geometri. Mengajar selama 10 tahun.',
                'position' => 'Guru Matematika',
                'employee_id' => '197505151998021001',
                'is_active' => true,
                'last_login' => now()->subHours(1),
            ],
            [
                'name' => 'Sari Indah, S.Pd',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'guru',
                'phone' => '081234567892',
                'bio' => 'Guru Bahasa Indonesia yang passionate dalam mengembangkan kemampuan literasi siswa.',
                'position' => 'Guru Bahasa Indonesia',
                'employee_id' => '198203102005012002',
                'is_active' => true,
                'last_login' => now()->subMinutes(30),
            ],
            [
                'name' => 'Budi Santoso, S.Kom',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'guru',
                'phone' => '081234567893',
                'bio' => 'Guru TIK yang menguasai berbagai teknologi pembelajaran modern.',
                'position' => 'Guru Teknologi Informasi',
                'employee_id' => '198512252010011003',
                'is_active' => true,
                'last_login' => now()->subDays(1),
            ],
            [
                'name' => 'Rina Marlina, S.E',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'staff',
                'phone' => '081234567894',
                'bio' => 'Staff Tata Usaha yang menangani administrasi keuangan dan kepegawaian.',
                'position' => 'Staff Tata Usaha',
                'employee_id' => '198907142012012004',
                'is_active' => true,
                'last_login' => now()->subHours(3),
            ],
            [
                'name' => 'Dedi Kurniawan',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'staff',
                'phone' => '081234567895',
                'bio' => 'Staff kebersihan dan keamanan sekolah.',
                'position' => 'Staff Kebersihan',
                'employee_id' => '197812101995011005',
                'is_active' => true,
                'last_login' => now()->subDays(2),
            ],
            [
                'name' => 'Maya Sari',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'user',
                'phone' => '081234567896',
                'bio' => 'User biasa untuk testing sistem.',
                'position' => 'User',
                'employee_id' => null,
                'is_active' => true,
                'last_login' => now()->subWeek(),
            ],
            [
                'name' => 'Inactive User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'user',
                'phone' => '081234567897',
                'bio' => 'User yang dinonaktifkan untuk testing.',
                'position' => 'Inactive User',
                'employee_id' => null,
                'is_active' => false,
                'last_login' => now()->subMonth(),
            ],
        ];

        foreach ($users as $userData) {
            User::create($userData);
        }

        $this->command->info('✅ Sample users created successfully!');
        $this->command->info('📊 Total users: ' . User::count());
        $this->command->info('👥 Active users: ' . User::where('is_active', true)->count());
        $this->command->info('🔒 Inactive users: ' . User::where('is_active', false)->count());
    }
}
