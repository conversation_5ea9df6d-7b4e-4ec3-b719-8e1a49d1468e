<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'name' => 'Dr. <PERSON>, M.<PERSON>d',
                'email' => '<EMAIL>',
                'phone' => '081234567890',
                'password' => Hash::make('password123'),
                'role' => 'kepala_sekolah',
                'position' => 'Kepala Sekolah',
                'employee_id' => '196501011990031001',
                'bio' => 'Kepala Sekolah SMAN 1 Jakarta dengan pengalaman 25 tahun di bidang pendidikan.',
                'is_active' => true,
                'last_login' => now()->subHours(2),
            ],
            [
                'name' => '<PERSON><PERSON>, S.Pd',
                'email' => '<EMAIL>',
                'phone' => '081234567891',
                'password' => Hash::make('password123'),
                'role' => 'guru',
                'position' => 'Guru Matematika',
                'employee_id' => '197803152005012001',
                'bio' => 'Guru Matematika dengan spesialisasi Aljabar dan Geometri.',
                'is_active' => true,
                'last_login' => now()->subHours(5),
            ],
            [
                'name' => 'Budi Santoso',
                'email' => '<EMAIL>',
                'phone' => '081234567892',
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'position' => 'Administrator Sistem',
                'employee_id' => '198205102010011001',
                'bio' => 'Administrator sistem dan teknologi informasi sekolah.',
                'is_active' => true,
                'last_login' => now()->subMinutes(30),
            ],
            [
                'name' => 'Maya Sari, S.Pd',
                'email' => '<EMAIL>',
                'phone' => '081234567893',
                'password' => Hash::make('password123'),
                'role' => 'guru',
                'position' => 'Guru Bahasa Indonesia',
                'employee_id' => '198512202008012002',
                'bio' => 'Guru Bahasa Indonesia dengan fokus pada sastra dan linguistik.',
                'is_active' => false,
                'last_login' => now()->subDays(15),
            ],
            [
                'name' => 'Andi Pratama',
                'email' => '<EMAIL>',
                'phone' => '081234567894',
                'password' => Hash::make('password123'),
                'role' => 'staff',
                'position' => 'Staff Tata Usaha',
                'employee_id' => '199001152015031001',
                'bio' => 'Staff administrasi dan tata usaha sekolah.',
                'is_active' => true,
                'last_login' => now()->subDays(1),
            ],
            [
                'name' => 'Dewi Lestari, S.Pd',
                'email' => '<EMAIL>',
                'phone' => '081234567895',
                'password' => Hash::make('password123'),
                'role' => 'guru',
                'position' => 'Guru Fisika',
                'employee_id' => '198807102012012001',
                'bio' => 'Guru Fisika dengan pengalaman mengajar 12 tahun.',
                'is_active' => true,
                'last_login' => now()->subHours(8),
            ],
        ];

        foreach ($users as $userData) {
            User::create($userData);
        }
    }
}
