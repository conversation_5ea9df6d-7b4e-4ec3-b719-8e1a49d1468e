<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Http\Controllers\UserController;
use Illuminate\Http\Request;

try {
    echo "=== TESTING USER CONTROLLER ===\n";
    
    // Test 1: Create controller instance
    echo "1. Creating UserController instance...\n";
    $controller = new UserController();
    echo "   Controller created successfully\n";
    
    // Test 2: Create mock request
    echo "2. Creating mock request...\n";
    $request = new Request();
    echo "   Request created successfully\n";
    
    // Test 3: Call index method
    echo "3. Calling index method...\n";
    $response = $controller->index($request);
    echo "   Index method called successfully\n";
    
    // Test 4: Check response
    echo "4. Checking response...\n";
    $responseData = $response->getData(true);
    echo "   Response success: " . ($responseData['success'] ? 'Yes' : 'No') . "\n";
    echo "   Data count: " . count($responseData['data']) . "\n";
    
    if (!empty($responseData['data'])) {
        $firstUser = $responseData['data'][0];
        echo "   First user: {$firstUser['name']} ({$firstUser['email']})\n";
        echo "   Role: {$firstUser['role']}\n";
    }
    
    echo "\n=== CONTROLLER TEST PASSED ===\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "\nStack trace:\n" . $e->getTraceAsString() . "\n";
}
