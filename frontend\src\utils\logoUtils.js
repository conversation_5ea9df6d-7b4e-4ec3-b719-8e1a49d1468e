/**
 * Utility functions for handling school logo operations with database
 */

import { api } from '../services/api';

// Default logo path (relative to public folder)
export const DEFAULT_LOGO_PATH = '/images/logo-school.png';

// Logo upload folder path
export const LOGO_UPLOAD_PATH = '/images/logo/';

/**
 * Validate uploaded logo file
 * @param {File} file - The uploaded file
 * @returns {Object} - Validation result with success and message
 */
export const validateLogoFile = (file) => {
  if (!file) {
    return { success: false, message: 'Tidak ada file yang dipilih' };
  }

  // Validate file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  if (!allowedTypes.includes(file.type)) {
    return {
      success: false,
      message: 'Tipe file tidak didukung. Gunakan JPG, PNG, atau WebP'
    };
  }

  // Validate file size (max 5MB)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    return {
      success: false,
      message: 'Ukuran file terlalu besar. Maksimal 5MB'
    };
  }

  return { success: true, message: 'File valid' };
};

/**
 * Generate unique filename for logo
 * @param {File} file - The uploaded file
 * @returns {string} - Unique filename
 */
export const generateLogoFilename = (file) => {
  const timestamp = Date.now();
  const extension = file.name.split('.').pop();
  return `school-logo-${timestamp}.${extension}`;
};

/**
 * Create preview URL for uploaded file
 * @param {File} file - The uploaded file
 * @returns {Promise<string>} - Preview URL
 */
export const createLogoPreview = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(e);
    reader.readAsDataURL(file);
  });
};

/**
 * Upload logo to database via API
 * @param {File} file - The uploaded file
 * @param {string} description - Optional description
 * @returns {Promise<Object>} - The upload result
 */
export const uploadLogoToDatabase = async (file, description = '') => {
  try {
    // Validate file first
    const validation = validateLogoFile(file);
    if (!validation.success) {
      throw new Error(validation.message);
    }

    // Create FormData for file upload
    const formData = new FormData();
    formData.append('logo', file);
    if (description) {
      formData.append('description', description);
    }

    // Upload to API
    const response = await api.post('/logo/upload', formData);

    if (response.data.success) {
      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };
    } else {
      throw new Error(response.data.message || 'Upload failed');
    }

  } catch (error) {
    console.error('Error uploading logo:', error);
    return {
      success: false,
      message: error.response?.data?.message || error.message || 'Gagal mengupload logo'
    };
  }
};

/**
 * Get current active logo from database
 * @returns {Promise<Object|null>} - Current logo data or null
 */
export const getCurrentLogo = async () => {
  try {
    const response = await api.get('/logo/current');

    if (response.data.success) {
      return response.data.data;
    }

    return null;
  } catch {
    // Silently fallback to null
    return null;
  }
};

/**
 * Get latest uploaded logo from database
 * @returns {Promise<Object|null>} - Latest logo data or null
 */
export const getLatestLogo = async () => {
  try {
    const response = await api.get('/logo/current');

    if (response.data.success) {
      return response.data.data;
    }

    return null;
  } catch {
    // Return null to use fallback logo
    return null;
  }
};

/**
 * Get all logos from database
 * @returns {Promise<Array>} - Array of all logos
 */
export const getAllLogos = async () => {
  try {
    const response = await api.get('/logo/all');

    if (response.data.success) {
      return response.data.data;
    }

    return [];
  } catch {
    return [];
  }
};

/**
 * Delete logo from database
 * @param {number} logoId - ID of logo to delete
 * @returns {Promise<Object>} - Delete result
 */
export const deleteLogo = async (logoId) => {
  try {
    const response = await api.delete(`/logos/${logoId}`);

    if (response.data.success) {
      // Clear cache
      sessionStorage.removeItem('cachedLogoUrl');
      return {
        success: true,
        message: response.data.message
      };
    }

    throw new Error(response.data.message || 'Delete failed');
  } catch (error) {
    console.error('Error deleting logo:', error);
    return {
      success: false,
      message: error.response?.data?.message || error.message || 'Gagal menghapus logo'
    };
  }
};

/**
 * Set logo as active
 * @param {number} logoId - ID of logo to set as active
 * @returns {Promise<Object>} - Result
 */
export const setActiveLogo = async (logoId) => {
  try {
    const response = await api.put(`/logos/${logoId}/activate`);

    if (response.data.success) {
      // Clear cache to force refresh
      sessionStorage.removeItem('cachedLogoUrl');
      return {
        success: true,
        data: response.data.data,
        message: response.data.message
      };
    }

    throw new Error(response.data.message || 'Failed to set active');
  } catch (error) {
    console.error('Error setting active logo:', error);
    return {
      success: false,
      message: error.response?.data?.message || error.message || 'Gagal mengatur logo aktif'
    };
  }
};

/**
 * Get logo URL for display (async version for database)
 * @returns {Promise<string>} - Logo URL to display
 */
export const getLogoUrl = async () => {
  try {
    // Try to get from cache first for immediate display
    const cachedUrl = sessionStorage.getItem('cachedLogoUrl');
    if (cachedUrl && cachedUrl !== DEFAULT_LOGO_PATH) {
      // Return cached URL immediately, but also refresh in background
      refreshLogoCache();
      return cachedUrl;
    }

    // Get latest logo from database
    const latestLogo = await getLatestLogo();
    if (latestLogo && latestLogo.url) {
      sessionStorage.setItem('cachedLogoUrl', latestLogo.url);
      return latestLogo.url;
    }

    // Fallback to current logo
    const currentLogo = await getCurrentLogo();
    if (currentLogo && currentLogo.url) {
      sessionStorage.setItem('cachedLogoUrl', currentLogo.url);
      return currentLogo.url;
    }

    // Final fallback to default logo
    return DEFAULT_LOGO_PATH;
  } catch (error) {
    console.error('Error getting logo URL:', error);

    // Try cache as fallback
    const cachedUrl = sessionStorage.getItem('cachedLogoUrl');
    if (cachedUrl) {
      return cachedUrl;
    }

    return DEFAULT_LOGO_PATH;
  }
};

/**
 * Refresh logo cache in background
 */
const refreshLogoCache = async () => {
  try {
    const latestLogo = await getLatestLogo();
    if (latestLogo && latestLogo.url) {
      sessionStorage.setItem('cachedLogoUrl', latestLogo.url);
    }
  } catch (error) {
    console.error('Error refreshing logo cache:', error);
  }
};

/**
 * Get all logo files from database
 * @returns {Promise<Array>} - Array of logo file information
 */
export const getLogoFilesFromDatabase = async () => {
  try {
    const logos = await getAllLogos();

    // Add default logo to the list
    const defaultLogoInfo = {
      id: 'default',
      filename: 'logo-school.png',
      original_name: 'logo-school.png',
      url: DEFAULT_LOGO_PATH,
      file_size: 0,
      formatted_size: '0 KB',
      mime_type: 'image/png',
      uploaded_at: '2024-01-01 00:00:00',
      is_active: logos.length === 0, // Default is active if no logos exist
      isDefault: true
    };

    return [defaultLogoInfo, ...logos];
  } catch (error) {
    console.error('Error getting logos from database:', error);
    return [{
      id: 'default',
      filename: 'logo-school.png',
      original_name: 'logo-school.png',
      url: DEFAULT_LOGO_PATH,
      file_size: 0,
      formatted_size: '0 KB',
      mime_type: 'image/png',
      uploaded_at: '2024-01-01 00:00:00',
      is_active: true,
      isDefault: true
    }];
  }
};

/**
 * Preload logo to prevent flickering (async version)
 * @returns {Promise<string>} - Logo URL
 */
export const preloadLogo = async () => {
  try {
    const logoUrl = await getLogoUrl();

    if (logoUrl === DEFAULT_LOGO_PATH) {
      return logoUrl;
    }

    // Create image element to preload
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(logoUrl);
      img.onerror = () => resolve(DEFAULT_LOGO_PATH);
      img.src = logoUrl;

      // Timeout fallback
      setTimeout(() => resolve(logoUrl), 2000);
    });
  } catch (error) {
    console.error('Error preloading logo:', error);
    return DEFAULT_LOGO_PATH;
  }
};
