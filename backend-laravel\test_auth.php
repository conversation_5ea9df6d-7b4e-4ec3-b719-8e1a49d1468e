<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Laravel\Sanctum\PersonalAccessToken;

try {
    echo "=== TESTING AUTHENTICATION ===\n";
    
    // Test 1: Get token from frontend (replace with actual token)
    $tokenFromFrontend = '7|Q2yaLzzsdkYq3enGed'; // First part of token from frontend
    echo "1. Testing token: {$tokenFromFrontend}...\n";
    
    // Test 2: Find token in database
    echo "2. Looking for token in database...\n";
    $tokens = PersonalAccessToken::where('token', 'like', hash('sha256', $tokenFromFrontend) . '%')->get();
    echo "   Found tokens: " . $tokens->count() . "\n";
    
    if ($tokens->count() > 0) {
        $token = $tokens->first();
        echo "   Token ID: " . $token->id . "\n";
        echo "   Token name: " . $token->name . "\n";
        echo "   Tokenable ID: " . $token->tokenable_id . "\n";
        echo "   Created: " . $token->created_at . "\n";
        
        // Test 3: Get user from token
        echo "3. Getting user from token...\n";
        $user = $token->tokenable;
        if ($user) {
            echo "   User ID: " . $user->id . "\n";
            echo "   User name: " . $user->name . "\n";
            echo "   User email: " . $user->email . "\n";
            echo "   User role: " . $user->role . "\n";
            echo "   Is admin: " . ($user->isAdmin() ? 'Yes' : 'No') . "\n";
            echo "   Is active: " . ($user->is_active ? 'Yes' : 'No') . "\n";
        } else {
            echo "   No user found for token!\n";
        }
    } else {
        echo "   Token not found in database!\n";
        
        // List all tokens
        echo "4. Listing all tokens...\n";
        $allTokens = PersonalAccessToken::with('tokenable')->get();
        foreach ($allTokens as $token) {
            echo "   Token ID: {$token->id}, User: {$token->tokenable->name}, Role: {$token->tokenable->role}\n";
        }
    }
    
    echo "\n=== TEST COMPLETED ===\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
