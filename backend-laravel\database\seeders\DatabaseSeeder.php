<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\News;
use App\Models\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'is_active' => true,
        ]);

        echo "✅ Admin user created (email: <EMAIL>, password: admin123)\n";

        // Create sample news
        $sampleNews = [
            [
                'title' => 'Penerimaan Siswa Baru Tahun Ajaran 2025/2026',
                'content' => 'SMA Negeri 1 Jakarta membuka pendaftaran siswa baru untuk tahun ajaran 2025/2026. Pendaftaran dibuka mulai tanggal 1 Februari hingga 28 Februari 2025. Calon siswa dapat mendaftar secara online melalui website resmi sekolah.',
                'excerpt' => 'Pendaftaran siswa baru dibuka mulai 1 Februari 2025',
                'image_url' => '/uploads/news/psb-2025.jpg',
                'author_id' => $admin->id,
                'published' => true,
                'featured' => true,
                'tags' => ['pendaftaran', 'siswa baru', 'PSB'],
                'published_at' => now(),
            ],
            [
                'title' => 'Prestasi Siswa dalam Olimpiade Matematika Nasional',
                'content' => 'Siswa SMA Negeri 1 Jakarta berhasil meraih medali emas dalam Olimpiade Matematika Nasional 2024. Prestasi ini membanggakan dan menunjukkan kualitas pendidikan di sekolah kami.',
                'excerpt' => 'Siswa meraih medali emas Olimpiade Matematika Nasional',
                'image_url' => '/uploads/news/olimpiade-math.jpg',
                'author_id' => $admin->id,
                'published' => true,
                'tags' => ['prestasi', 'olimpiade', 'matematika'],
                'published_at' => now()->subDays(1),
            ],
        ];

        foreach ($sampleNews as $newsData) {
            News::create($newsData);
        }

        echo "✅ Sample news created\n";

        // Create school settings
        $schoolSettings = [
            ['key' => 'schoolName', 'value' => 'SMA Negeri 1 Jakarta', 'type' => 'string', 'category' => 'general', 'description' => 'Nama lengkap sekolah'],
            ['key' => 'schoolShortName', 'value' => 'SMAN 1 Jakarta', 'type' => 'string', 'category' => 'general', 'description' => 'Nama singkat sekolah'],
            ['key' => 'schoolEmail', 'value' => '<EMAIL>', 'type' => 'string', 'category' => 'contact', 'description' => 'Email resmi sekolah'],
            ['key' => 'schoolPhone', 'value' => '021-12345678', 'type' => 'string', 'category' => 'contact', 'description' => 'Nomor telepon sekolah'],
            ['key' => 'schoolAddress', 'value' => 'Jl. Pendidikan No. 123, Menteng, Jakarta Pusat, DKI Jakarta 10310', 'type' => 'string', 'category' => 'contact', 'description' => 'Alamat lengkap sekolah'],
            ['key' => 'schoolWebsite', 'value' => 'https://www.sman1jakarta.sch.id', 'type' => 'string', 'category' => 'contact', 'description' => 'Website resmi sekolah'],
            ['key' => 'principalName', 'value' => 'Dr. Ahmad Suryadi, M.Pd', 'type' => 'string', 'category' => 'general', 'description' => 'Nama kepala sekolah'],
            ['key' => 'schoolMotto', 'value' => 'Unggul dalam Prestasi, Berkarakter, dan Berwawasan Global', 'type' => 'string', 'category' => 'general', 'description' => 'Motto sekolah'],
            ['key' => 'schoolDescription', 'value' => 'SMA Negeri 1 Jakarta adalah sekolah menengah atas negeri yang berkomitmen untuk memberikan pendidikan berkualitas tinggi dengan mengembangkan potensi akademik dan karakter siswa.', 'type' => 'string', 'category' => 'general', 'description' => 'Deskripsi sekolah'],
            ['key' => 'logoUrl', 'value' => '/images/logo-school.png', 'type' => 'string', 'category' => 'appearance', 'description' => 'URL logo sekolah']
        ];

        foreach ($schoolSettings as $settingData) {
            Setting::updateOrCreate(
                ['key' => $settingData['key']],
                [
                    'value' => $settingData['value'],
                    'type' => $settingData['type'],
                    'category' => $settingData['category'],
                    'description' => $settingData['description'],
                    'is_public' => true,
                    'updated_by' => $admin->id,
                ]
            );
        }

        echo "✅ School settings created\n";
        echo "🎉 Database seeding completed successfully!\n";
    }
}
