import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useSchoolSettings } from '../hooks/useSchoolSettings';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { settings, loading } = useSchoolSettings();

  return (
    <>
      {/* Top Navbar - Social Media & Address */}
      <div className="bg-blue-800 text-white text-xs py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">

            {/* Social Media Links - Left side - Icon Only */}
            <div className="flex items-center space-x-3">

              {/* Instagram */}
              <a
                href="https://instagram.com"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-200 transition-colors duration-200"
                title="Instagram"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>

              {/* YouTube */}
              <a
                href="https://youtube.com"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-200 transition-colors duration-200"
                title="YouTube"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </a>

              {/* Facebook */}
              <a
                href="https://facebook.com"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-200 transition-colors duration-200"
                title="Facebook"
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>

              {/* Email */}
              <a
                href="mailto:<EMAIL>"
                className="hover:text-blue-200 transition-colors duration-200"
                title="Email"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </a>
            </div>

            {/* Address - Right side */}
            <div className="flex items-center justify-center min-[900px]:justify-end space-x-2 text-blue-200">
              <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span className="text-center min-[900px]:text-right">
                {!loading && settings.schoolAddress ? (
                  <>
                    {/* Mobile: 8 huruf + ... */}
                    <span className="block min-[900px]:hidden">
                      {settings.schoolAddress.length > 8 ?
                        `${settings.schoolAddress.substring(0, 8)}...` :
                        settings.schoolAddress
                      }
                    </span>
                    {/* Desktop: 60 huruf + ... */}
                    <span className="hidden min-[900px]:inline">
                      {settings.schoolAddress.length > 60 ?
                        `${settings.schoolAddress.substring(0, 60)}...` :
                        settings.schoolAddress
                      }
                    </span>
                  </>
                ) : loading ? (
                  <span className="block min-[900px]:inline text-blue-300">
                    Memuat...
                  </span>
                ) : (
                  <>
                    {/* Mobile fallback: 8 huruf + ... */}
                    <span className="block min-[900px]:hidden">
                      Jl. Pend...
                    </span>
                    {/* Desktop fallback: alamat lengkap */}
                    <span className="hidden min-[900px]:inline">
                      Jl. Pendidikan No. 123, Jakarta Selatan
                    </span>
                  </>
                )}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Navbar */}
      <nav className="bg-blue-600 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link to="/" className="flex items-center text-white text-xl font-bold">
              {!loading && (
                <>
                  <img
                    src={settings.logoUrl}
                    alt={`Logo ${settings.schoolShortName}`}
                    className="h-8 w-8 mr-3 object-contain"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                  <span>{settings.schoolShortName}</span>
                </>
              )}
              {loading && (
                <span>Loading...</span>
              )}
            </Link>
          </div>

          {/* Desktop Menu - Horizontal text links at the right (visible on screens 900px+) */}
          <div className="hidden min-[900px]:flex items-center space-x-8">
            <Link
              to="/"
              className="text-white hover:text-blue-200 text-sm font-medium transition duration-300"
            >
              Home
            </Link>
            <Link
              to="/about"
              className="text-white hover:text-blue-200 text-sm font-medium transition duration-300"
            >
              About
            </Link>
            <Link
              to="/news"
              className="text-white hover:text-blue-200 text-sm font-medium transition duration-300"
            >
              Berita
            </Link>
            <Link
              to="/gallery"
              className="text-white hover:text-blue-200 text-sm font-medium transition duration-300"
            >
              Galeri
            </Link>
            <Link
              to="/contact"
              className="text-white hover:text-blue-200 text-sm font-medium transition duration-300"
            >
              Kontak
            </Link>
            <Link
              to="/students"
              className="text-white hover:text-blue-200 text-sm font-medium transition duration-300"
            >
              Students
            </Link>
          </div>

          {/* Mobile menu button (visible below 900px only) */}
          <div className="min-[900px]:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-white hover:text-blue-200 focus:outline-none p-2"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Sidebar Overlay */}
        {isOpen && (
          <div className="min-[900px]:hidden fixed inset-0 z-[99999]">
            {/* Background Overlay */}
            <div
              className={`fixed inset-0 bg-black transition-all duration-500 ease-out ${
                isOpen ? 'bg-opacity-50' : 'bg-opacity-0 pointer-events-none'
              }`}
              onClick={() => setIsOpen(false)}
            ></div>

            {/* Sidebar */}
            <div className={`fixed top-0 right-0 h-full w-80 bg-blue-600 shadow-2xl transform transition-all duration-500 ease-out ${
              isOpen ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
            }`}>

              {/* Sidebar Header */}
              <div className="bg-blue-700 px-6 py-4 flex items-center justify-between border-b border-blue-500">
                <div className="flex items-center">
                  {!loading && settings.logoUrl && (
                    <img
                      src={settings.logoUrl}
                      alt={`Logo ${settings.schoolShortName}`}
                      className="h-8 w-8 mr-3 object-contain"
                      onError={(e) => {
                        e.target.style.display = 'none';
                      }}
                    />
                  )}
                  <div>
                    <h2 className="text-white font-bold text-lg">
                      {loading ? 'Loading...' : settings.schoolShortName}
                    </h2>
                    <p className="text-blue-200 text-xs">Menu Navigasi</p>
                  </div>
                </div>

                {/* Close Button */}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-white hover:text-blue-200 p-2 rounded-full hover:bg-blue-800 transition-colors duration-200"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* Navigation Links */}
              <div className="py-6">
                <nav className="space-y-2 px-4">
                  <Link
                    to="/"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                    <span className="font-medium">Beranda</span>
                  </Link>

                  <Link
                    to="/about"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">Tentang Kami</span>
                  </Link>

                  <Link
                    to="/news"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                    </svg>
                    <span className="font-medium">Berita</span>
                  </Link>

                  <Link
                    to="/gallery"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="font-medium">Galeri</span>
                  </Link>

                  <Link
                    to="/contact"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="font-medium">Kontak</span>
                  </Link>

                  <Link
                    to="/students"
                    className="flex items-center px-4 py-3 text-white hover:bg-blue-800 hover:bg-opacity-50 rounded-lg transition-all duration-200 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg className="w-5 h-5 mr-3 text-blue-200 group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    <span className="font-medium">Siswa</span>
                  </Link>
                </nav>


              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
    </>
  );
};

export default Navbar;
