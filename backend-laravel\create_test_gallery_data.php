<?php

/**
 * Script untuk membuat data test gallery dengan berbagai kategori
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== CREATING TEST GALLERY DATA ===\n\n";

$validCategories = ['Fasilitas', 'Kegiatan', 'Prestasi', 'Ekstrakurikuler', 'Umum'];
// Use relative path for images (no need for full URL)
$imagePath = '/images/gallery';

// Create test data for each category
$testData = [
    [
        'title' => 'Perpustakaan Sekolah',
        'description' => 'Fasilitas perpustakaan yang lengkap dengan berbagai koleksi buku',
        'category' => 'Fasilitas',
        'image_url' => "{$imagePath}/gallery_1753243029.jpg",
        'uploaded_by' => 1,
        'featured' => true,
        'is_active' => true,
        'sort_order' => 1,
        'created_at' => now(),
        'updated_at' => now()
    ],
    [
        'title' => 'Upacara Bendera',
        'description' => 'Kegiatan upacara bendera setiap hari Senin',
        'category' => 'Kegiatan',
        'image_url' => "{$imagePath}/gallery_1753243054.png",
        'uploaded_by' => 1,
        'featured' => false,
        'is_active' => true,
        'sort_order' => 2,
        'created_at' => now(),
        'updated_at' => now()
    ],
    [
        'title' => 'Juara Olimpiade Matematika',
        'description' => 'Prestasi siswa dalam olimpiade matematika tingkat provinsi',
        'category' => 'Prestasi',
        'image_url' => "{$imagePath}/gallery_1753243125.png",
        'uploaded_by' => 1,
        'featured' => true,
        'is_active' => true,
        'sort_order' => 3,
        'created_at' => now(),
        'updated_at' => now()
    ],
    [
        'title' => 'Klub Robotika',
        'description' => 'Kegiatan ekstrakurikuler robotika siswa',
        'category' => 'Ekstrakurikuler',
        'image_url' => "{$imagePath}/gallery_1753243286.jpg",
        'uploaded_by' => 1,
        'featured' => false,
        'is_active' => true,
        'sort_order' => 4,
        'created_at' => now(),
        'updated_at' => now()
    ],
    [
        'title' => 'Kegiatan Umum Sekolah',
        'description' => 'Berbagai kegiatan umum di lingkungan sekolah',
        'category' => 'Umum',
        'image_url' => "{$imagePath}/gallery_1753321778.jpg",
        'uploaded_by' => 1,
        'featured' => false,
        'is_active' => true,
        'sort_order' => 5,
        'created_at' => now(),
        'updated_at' => now()
    ]
];

echo "1. Creating test gallery data...\n";

foreach ($testData as $data) {
    try {
        // Check if similar data already exists
        $existing = DB::table('galleries')
            ->where('title', $data['title'])
            ->first();
        
        if ($existing) {
            echo "⚠️  Data already exists: {$data['title']} (ID: {$existing->id})\n";
            continue;
        }
        
        $id = DB::table('galleries')->insertGetId($data);
        echo "✅ Created: {$data['title']} (ID: {$id}) - Category: {$data['category']}\n";
        
    } catch (Exception $e) {
        echo "❌ Error creating {$data['title']}: " . $e->getMessage() . "\n";
    }
}

echo "\n2. Current gallery data:\n";
$galleries = DB::table('galleries')
    ->select('id', 'title', 'category', 'featured', 'is_active')
    ->orderBy('sort_order')
    ->get();

foreach ($galleries as $gallery) {
    $featured = $gallery->featured ? '⭐' : '';
    $active = $gallery->is_active ? '✅' : '❌';
    echo "{$active} ID: {$gallery->id} | {$gallery->title} | Category: {$gallery->category} {$featured}\n";
}

echo "\n3. Category distribution:\n";
foreach ($validCategories as $category) {
    $count = DB::table('galleries')->where('category', $category)->count();
    echo "📊 {$category}: {$count} items\n";
}

echo "\n=== TEST DATA CREATED ===\n";
echo "Now you can test the edit modal with different categories!\n\n";

echo "To test:\n";
echo "1. Go to frontend gallery management\n";
echo "2. Click edit on any gallery item\n";
echo "3. Check if the category dropdown shows the correct selected value\n";
echo "4. Try changing categories and saving\n\n";
