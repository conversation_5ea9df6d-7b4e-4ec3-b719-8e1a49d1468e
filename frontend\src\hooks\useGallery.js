import { useState, useEffect } from 'react';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export const useGallery = (category = 'all', featured = false) => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchGallery = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Build query parameters
        const params = new URLSearchParams();
        if (category && category !== 'all') {
          params.append('category', category);
        }
        if (featured) {
          params.append('featured', 'true');
        }
        
        const url = `${API_BASE_URL}/gallery${params.toString() ? `?${params.toString()}` : ''}`;
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
          setImages(data.data || []);
        } else {
          throw new Error(data.message || 'Failed to fetch gallery');
        }
      } catch (err) {
        console.error('Error fetching gallery:', err);
        setError(err.message);

        // Jika API gagal, set images kosong
        setImages([]);
      } finally {
        setLoading(false);
      }
    };

    fetchGallery();
  }, [category, featured]);

  return { images, loading, error };
};

export const useFeaturedGallery = () => {
  return useGallery('all', true);
};

export const useCarouselGallery = (refreshTrigger = 0) => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchCarouselImages = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/gallery/carousel`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        console.log('Carousel images from API:', data.data); // Debug log

        // Transform data untuk memastikan path gambar sesuai
        const transformedImages = data.data.map(image => ({
          ...image,
          // Pastikan path gambar konsisten
          url: image.image_url,
          path: image.image_url
        }));

        setImages(transformedImages);
      } else {
        throw new Error(data.message || 'Failed to fetch carousel images');
      }
    } catch (err) {
      console.error('Error fetching carousel images:', err);
      setError(err.message);

      // Jika API gagal, set images kosong
      setImages([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCarouselImages();
  }, [refreshTrigger]);

  // Return function untuk refresh manual
  const refreshCarousel = () => {
    fetchCarouselImages();
  };

  return { images, loading, error, refreshCarousel };
};
