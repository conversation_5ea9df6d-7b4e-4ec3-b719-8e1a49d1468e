<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as DB;

// Setup database connection
$capsule = new DB;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'school_management',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

echo "=== SETTING CAROUSEL DATA ===\n\n";

try {
    // Set beberapa gambar untuk carousel
    $carouselIds = [9, 15, 16]; // Perpustakaan, Upacara Bendera, Juara Olimpiade
    
    // Reset semua carousel_pinned ke false
    DB::table('galleries')->update(['carousel_pinned' => false]);
    echo "1. Reset all carousel_pinned to false\n";
    
    // Set carousel_pinned untuk ID yang dipilih
    foreach ($carouselIds as $index => $id) {
        $updated = DB::table('galleries')
            ->where('id', $id)
            ->update([
                'carousel_pinned' => true,
                'sort_order' => $index + 1
            ]);
        
        if ($updated) {
            $gallery = DB::table('galleries')->where('id', $id)->first();
            echo "✅ Set carousel_pinned for ID {$id}: {$gallery->title} (sort_order: " . ($index + 1) . ")\n";
        } else {
            echo "❌ Failed to update ID {$id}\n";
        }
    }
    
    echo "\n2. Current carousel data:\n";
    $carouselImages = DB::table('galleries')
        ->where('carousel_pinned', true)
        ->where('is_active', true)
        ->orderBy('sort_order', 'asc')
        ->get();
    
    foreach ($carouselImages as $image) {
        echo "🎠 ID: {$image->id} | Title: {$image->title} | Category: {$image->category} | Sort: {$image->sort_order}\n";
        echo "   Image URL: {$image->image_url}\n";
    }
    
    echo "\n3. All gallery data:\n";
    $allImages = DB::table('galleries')
        ->orderBy('id', 'asc')
        ->get();
    
    foreach ($allImages as $image) {
        $carouselStatus = $image->carousel_pinned ? '🎠' : '  ';
        $featuredStatus = $image->featured ? '⭐' : '  ';
        echo "{$carouselStatus}{$featuredStatus} ID: {$image->id} | {$image->title} | {$image->category}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== CAROUSEL DATA SET COMPLETED ===\n";
