<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;

echo "=== USERS DATA ===\n";
echo "Total users: " . User::count() . "\n\n";

$users = User::all();
foreach ($users as $user) {
    echo "ID: " . $user->id . "\n";
    echo "Username: " . ($user->username ?? 'N/A') . "\n";
    echo "Name: " . ($user->name ?? 'N/A') . "\n";
    echo "Email: " . $user->email . "\n";
    echo "Role: " . $user->role . "\n";
    echo "Created: " . $user->created_at . "\n";
    echo "---\n";
}
