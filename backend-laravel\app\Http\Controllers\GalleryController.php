<?php

namespace App\Http\Controllers;

use App\Models\Gallery;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class GalleryController extends Controller
{
    /**
     * Get all gallery items (public)
     */
    public function index(Request $request)
    {
        $query = Gallery::with('uploader:id,name')
            ->active()
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc');

        // Category filter
        if ($request->has('category') && $request->category !== 'all') {
            $query->category($request->category);
        }

        // Featured filter
        if ($request->has('featured') && $request->boolean('featured')) {
            $query->featured();
        }

        // Carousel filter
        if ($request->has('carousel') && $request->boolean('carousel')) {
            $query->carouselPinned();
        }

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        $galleries = $query->paginate($request->get('per_page', 12));

        return response()->json([
            'success' => true,
            'data' => $galleries->items(),
            'pagination' => [
                'current_page' => $galleries->currentPage(),
                'last_page' => $galleries->lastPage(),
                'per_page' => $galleries->perPage(),
                'total' => $galleries->total(),
            ]
        ]);
    }

    /**
     * Get single gallery item by ID (public)
     */
    public function show($id)
    {
        $gallery = Gallery::with('uploader:id,name')
            ->active()
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $gallery
        ]);
    }

    /**
     * Get all gallery items for admin (including inactive) - Optimized
     */
    public function adminIndex(Request $request)
    {
        // Start with optimized query
        $query = Gallery::select([
                'id', 'title', 'description', 'image_url', 'category',
                'uploaded_by', 'is_active', 'featured', 'carousel_pinned',
                'sort_order', 'created_at', 'updated_at'
            ])
            ->with(['uploader:id,name'])
            ->orderBy('created_at', 'desc');

        // Search optimization - use index
        if ($request->has('search') && $request->search) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // Category filter - use index
        if ($request->has('category') && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // Status filter - use index
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Featured filter - use index
        if ($request->has('featured')) {
            if ($request->featured === 'true') {
                $query->where('featured', true);
            } elseif ($request->featured === 'false') {
                $query->where('featured', false);
            }
        }

        // Carousel filter - use index
        if ($request->has('carousel')) {
            if ($request->carousel === 'true') {
                $query->where('carousel_pinned', true);
            } elseif ($request->carousel === 'false') {
                $query->where('carousel_pinned', false);
            }
        }

        // Set pagination to 5 items per page for testing
        $perPage = min($request->get('per_page', 5), 50); // Default 5 items per page
        $galleries = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $galleries->items(),
            'pagination' => [
                'current_page' => $galleries->currentPage(),
                'last_page' => $galleries->lastPage(),
                'per_page' => $galleries->perPage(),
                'total' => $galleries->total(),
                'from' => $galleries->firstItem(),
                'to' => $galleries->lastItem(),
            ]
        ]);
    }

    /**
     * Store new gallery item (admin only)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'category' => 'required|in:Fasilitas,Kegiatan,Prestasi,Ekstrakurikuler,Umum',
            'featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        // Handle file upload to frontend public folder
        $imageUrl = null;
        if ($request->hasFile('image')) {
            $image = $request->file('image');

            // Create gallery record first to get ID
            $gallery = Gallery::create([
                'title' => $request->get('title'),
                'description' => $request->get('description'),
                'category' => $request->get('category'),
                'uploaded_by' => 1, // Default user ID
                'is_active' => $request->boolean('is_active', true),
                'featured' => $request->boolean('featured', false),
                'carousel_pinned' => $request->boolean('carousel_pinned', false),
                'sort_order' => $request->get('sort_order', 0),
                'image_url' => '', // Temporary, will be updated
            ]);

            // Generate filename with gallery ID: gallery_id_timestamp.ext
            $timestamp = time();
            $extension = $image->getClientOriginalExtension();
            $filename = "gallery_{$gallery->id}_{$timestamp}.{$extension}";

            // Define frontend gallery path (relative to backend)
            $frontendGalleryPath = base_path('../frontend/public/images/gallery');

            // Create directory if it doesn't exist
            if (!is_dir($frontendGalleryPath)) {
                mkdir($frontendGalleryPath, 0755, true);
            }

            // Move file to frontend public folder
            $image->move($frontendGalleryPath, $filename);

            // Update gallery record with correct image URL
            $imageUrl = "/images/gallery/{$filename}";
            $gallery->update(['image_url' => $imageUrl]);

            return response()->json([
                'success' => true,
                'message' => 'Gallery item created successfully',
                'data' => $gallery->load('uploader:id,name')
            ]);
        }

        // If no image uploaded, create gallery without image
        $gallery = Gallery::create([
            'title' => $request->title,
            'description' => $request->description,
            'image_url' => null,
            'category' => $request->category,
            'uploaded_by' => auth()->id() ?? 1, // Default to user ID 1 if no auth
            'featured' => $request->boolean('featured', false),
            'carousel_pinned' => $request->boolean('carousel_pinned', false),
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->get('sort_order', 0),
        ]);

        $gallery->load('uploader:id,name');

        return response()->json([
            'success' => true,
            'message' => 'Gallery item created successfully (no image)',
            'data' => $gallery
        ], 201);
    }

    /**
     * Update gallery item (admin only)
     */
    public function update(Request $request, $id)
    {
        $gallery = Gallery::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'category' => 'required|in:Kegiatan Rutin,Kegiatan Khusus,Prestasi,Fasilitas,Ekstrakurikuler',
            'featured' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        $updateData = [
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'featured' => $request->boolean('featured', false),
            'carousel_pinned' => $request->boolean('carousel_pinned', false),
            'is_active' => $request->boolean('is_active', true),
            'sort_order' => $request->get('sort_order', 0),
        ];

        // Handle file upload if new image provided
        if ($request->hasFile('image')) {
            // Delete old image if exists (from frontend folder)
            if ($gallery->image_url) {
                $oldFilename = basename($gallery->image_url);
                $oldFilePath = base_path('../frontend/public/images/gallery/' . $oldFilename);
                if (file_exists($oldFilePath)) {
                    unlink($oldFilePath);
                }
            }

            $image = $request->file('image');

            // Generate filename with gallery ID: gallery_id_timestamp.ext
            $timestamp = time();
            $extension = $image->getClientOriginalExtension();
            $filename = "gallery_{$gallery->id}_{$timestamp}.{$extension}";

            // Define frontend gallery path
            $frontendGalleryPath = base_path('../frontend/public/images/gallery');

            // Create directory if it doesn't exist
            if (!is_dir($frontendGalleryPath)) {
                mkdir($frontendGalleryPath, 0755, true);
            }

            // Move file to frontend public folder
            $image->move($frontendGalleryPath, $filename);

            // Store relative path for frontend (without domain)
            $updateData['image_url'] = "/images/gallery/{$filename}";
        }

        $gallery->update($updateData);
        $gallery->load('uploader:id,name');

        return response()->json([
            'success' => true,
            'message' => 'Gallery item updated successfully',
            'data' => $gallery
        ]);
    }

    /**
     * Delete gallery item (admin only)
     */
    public function destroy($id)
    {
        $gallery = Gallery::findOrFail($id);

        // Delete image file if exists (from frontend folder)
        if ($gallery->image_url) {
            $filename = basename($gallery->image_url);
            $filePath = base_path('../frontend/public/images/gallery/' . $filename);
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        $gallery->delete();

        return response()->json([
            'success' => true,
            'message' => 'Gallery item deleted successfully'
        ]);
    }

    /**
     * Pin/Unpin gallery item to carousel (admin only)
     */
    public function toggleCarouselPin($id)
    {
        $gallery = Gallery::findOrFail($id);

        $gallery->carousel_pinned = !$gallery->carousel_pinned;
        $gallery->save();

        return response()->json([
            'success' => true,
            'message' => $gallery->carousel_pinned ? 'Gambar berhasil di-pin ke carousel' : 'Gambar berhasil di-unpin dari carousel',
            'data' => $gallery
        ]);
    }

    /**
     * Get carousel images (public)
     */
    public function getCarouselImages()
    {
        $images = Gallery::with('uploader:id,name')
            ->active()
            ->carouselPinned()
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $images
        ]);
    }
}
