<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tambah settings default untuk website title dan favicon jika belum ada
        if (!DB::table('settings')->where('key', 'website_title')->exists()) {
            DB::table('settings')->insert([
                'key' => 'website_title',
                'value' => 'SMA Negeri 1 Jakarta',
                'type' => 'text',
                'group' => 'general',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        if (!DB::table('settings')->where('key', 'website_favicon')->exists()) {
            DB::table('settings')->insert([
                'key' => 'website_favicon',
                'value' => '/logo/default-favicon.png',
                'type' => 'file',
                'group' => 'general',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        if (!DB::table('settings')->where('key', 'meta_description')->exists()) {
            DB::table('settings')->insert([
                'key' => 'meta_description',
                'value' => 'Website resmi SMA Negeri 1 Jakarta',
                'type' => 'textarea',
                'group' => 'general',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('settings')->whereIn('key', ['website_title', 'website_favicon', 'meta_description'])->delete();
    }
};
