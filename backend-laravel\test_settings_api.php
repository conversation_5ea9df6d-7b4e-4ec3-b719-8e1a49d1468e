<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Setting;
use App\Http\Controllers\SettingsController;
use Illuminate\Http\Request;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Settings API Test ===\n\n";

// Test 1: Check database settings
echo "1. Checking settings in database...\n";
try {
    $allSettings = Setting::all();
    echo "✅ Total settings in database: " . $allSettings->count() . "\n";
    
    foreach ($allSettings as $setting) {
        $value = strlen($setting->value) > 50 ? substr($setting->value, 0, 50) . '...' : $setting->value;
        echo "   - {$setting->key}: {$value}\n";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Test getPublicSettings method
echo "2. Testing getPublicSettings method...\n";
try {
    $publicSettings = Setting::getPublicSettings();
    echo "✅ Public settings count: " . count($publicSettings) . "\n";
    
    foreach ($publicSettings as $key => $value) {
        $displayValue = strlen($value) > 50 ? substr($value, 0, 50) . '...' : $value;
        echo "   - {$key}: {$displayValue}\n";
    }
    
    // Check specifically for logoUrl
    if (isset($publicSettings['logoUrl'])) {
        $logoUrl = $publicSettings['logoUrl'];
        if (strpos($logoUrl, 'data:image') === 0) {
            echo "✅ logoUrl found: Base64 image (length: " . strlen($logoUrl) . ")\n";
        } else {
            echo "✅ logoUrl found: " . $logoUrl . "\n";
        }
    } else {
        echo "❌ logoUrl not found in public settings\n";
    }
} catch (Exception $e) {
    echo "❌ getPublicSettings error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test API endpoint directly
echo "3. Testing settings API endpoint...\n";
try {
    $controller = new SettingsController();
    $response = $controller->index();
    $responseData = json_decode($response->getContent(), true);
    
    if ($responseData['success']) {
        echo "✅ API endpoint successful\n";
        echo "📊 Data keys: " . implode(', ', array_keys($responseData['data'])) . "\n";
        
        if (isset($responseData['data']['logoUrl'])) {
            $logoUrl = $responseData['data']['logoUrl'];
            if (strpos($logoUrl, 'data:image') === 0) {
                echo "✅ API logoUrl: Base64 image (length: " . strlen($logoUrl) . ")\n";
            } else {
                echo "✅ API logoUrl: " . $logoUrl . "\n";
            }
        } else {
            echo "❌ logoUrl not found in API response\n";
        }
    } else {
        echo "❌ API endpoint failed\n";
    }
} catch (Exception $e) {
    echo "❌ API endpoint error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
