<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create or update admin user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'email' => '<EMAIL>',
                'phone' => '081234567890',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'position' => 'System Administrator',
                'employee_id' => 'ADM001',
                'bio' => 'System Administrator untuk aplikasi manajemen sekolah.',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        echo "Admin user created/updated successfully!\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n";
    }
}
