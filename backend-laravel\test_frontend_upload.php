<?php

/**
 * Test script untuk upload logo ke frontend folder
 * Run: php test_frontend_upload.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use App\Http\Controllers\SchoolLogoController;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING FRONTEND LOGO UPLOAD ===\n\n";

// Test 1: Check frontend folder structure
echo "1. Checking frontend folder structure...\n";
$frontendPath = base_path('../frontend/public/images/logo');
echo "Frontend logo path: {$frontendPath}\n";

if (!is_dir($frontendPath)) {
    echo "❌ Frontend logo directory does not exist\n";
    echo "Creating directory...\n";
    mkdir($frontendPath, 0755, true);
    echo "✅ Directory created\n";
} else {
    echo "✅ Frontend logo directory exists\n";
}

if (!is_writable($frontendPath)) {
    echo "❌ Frontend logo directory is not writable\n";
    exit(1);
} else {
    echo "✅ Frontend logo directory is writable\n\n";
}

// Test 2: Test upload to frontend folder
echo "2. Testing upload to frontend folder...\n";
try {
    // Create a simple test image content (1x1 pixel PNG)
    $pngData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==');
    
    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_logo') . '.png';
    file_put_contents($tempFile, $pngData);
    
    // Create UploadedFile instance
    $uploadedFile = new UploadedFile(
        $tempFile,
        'test_frontend_logo.png',
        'image/png',
        null,
        true // test mode
    );
    
    // Create request with file
    $request = new Request();
    $request->files->set('logo', $uploadedFile);
    $request->request->set('description', 'Test frontend logo upload');
    
    echo "📁 Test file created: {$tempFile}\n";
    echo "📊 File size: " . filesize($tempFile) . " bytes\n";
    
    // Test upload using controller
    $controller = new SchoolLogoController();
    $response = $controller->uploadLogo($request);
    $responseData = json_decode($response->getContent(), true);
    
    if ($response->getStatusCode() === 200 && $responseData['success']) {
        echo "✅ Upload successful!\n";
        echo "📁 Saved as: " . $responseData['data']['filename'] . "\n";
        echo "🗄️ Database ID: " . $responseData['data']['id'] . "\n";
        echo "📊 File size: " . $responseData['data']['formatted_size'] . "\n";
        echo "🔗 URL: " . $responseData['data']['url'] . "\n";
        echo "📂 File path: " . $responseData['data']['file_path'] . "\n\n";
        
        // Verify file exists in frontend folder
        $filename = $responseData['data']['filename'];
        $frontendFilePath = $frontendPath . '/' . $filename;
        
        if (file_exists($frontendFilePath)) {
            echo "✅ File exists in frontend folder: {$frontendFilePath}\n";
            echo "📊 Frontend file size: " . filesize($frontendFilePath) . " bytes\n";
        } else {
            echo "❌ File not found in frontend folder: {$frontendFilePath}\n";
        }
        
        // Verify database record
        $logoId = $responseData['data']['id'];
        $dbRecord = DB::table('school_logos')->where('id', $logoId)->first();
        if ($dbRecord) {
            echo "✅ Database record exists:\n";
            echo "  - ID: {$dbRecord->id}\n";
            echo "  - Filename: {$dbRecord->filename}\n";
            echo "  - Original Name: {$dbRecord->original_name}\n";
            echo "  - File Path: {$dbRecord->file_path}\n";
            echo "  - Is Active: {$dbRecord->is_active}\n";
            echo "  - Description: {$dbRecord->description}\n\n";
        } else {
            echo "❌ Database record not found\n\n";
        }
        
    } else {
        echo "❌ Upload failed\n";
        echo "Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";
    }
    
    // Clean up temp file
    if (file_exists($tempFile)) {
        unlink($tempFile);
        echo "🧹 Temporary file cleaned up\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error in upload test: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n\n";
}

// Test 3: List files in frontend logo directory
echo "3. Listing files in frontend logo directory...\n";
try {
    $files = scandir($frontendPath);
    $logoFiles = array_filter($files, function($file) {
        return !in_array($file, ['.', '..', '.gitkeep']);
    });
    
    echo "📁 Files in frontend logo directory: " . count($logoFiles) . "\n";
    foreach ($logoFiles as $file) {
        $filePath = $frontendPath . '/' . $file;
        $fileSize = filesize($filePath);
        echo "  - {$file} ({$fileSize} bytes)\n";
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ Error listing files: " . $e->getMessage() . "\n\n";
}

// Test 4: Test API endpoints
echo "4. Testing API endpoints...\n";
try {
    $controller = new SchoolLogoController();
    
    // Test getCurrentLogo
    $response = $controller->getCurrentLogo();
    $responseData = json_decode($response->getContent(), true);
    echo "✅ getCurrentLogo: " . ($responseData['success'] ? 'Success' : 'No logo found') . "\n";
    
    // Test getLatestLogo
    $response = $controller->getLatestLogo();
    $responseData = json_decode($response->getContent(), true);
    echo "✅ getLatestLogo: " . ($responseData['success'] ? 'Success' : 'No logo found') . "\n";
    
    // Test getAllLogos
    $response = $controller->getAllLogos();
    $responseData = json_decode($response->getContent(), true);
    $logoCount = $responseData['success'] ? count($responseData['data']) : 0;
    echo "✅ getAllLogos: {$logoCount} logos found\n\n";
    
} catch (Exception $e) {
    echo "❌ Error testing API endpoints: " . $e->getMessage() . "\n\n";
}

echo "=== TEST COMPLETED ===\n";
echo "Frontend logo upload system is ready!\n\n";

echo "File structure:\n";
echo "frontend/public/images/logo/ - Logo upload folder\n";
echo "frontend/public/images/ - Future folder for news/gallery images\n\n";

echo "Next steps:\n";
echo "1. Start frontend server: npm run dev (port 5173)\n";
echo "2. Start backend server: php artisan serve (port 8000)\n";
echo "3. Test upload from frontend admin panel\n";
echo "4. Check uploaded files in frontend/public/images/logo/\n\n";
