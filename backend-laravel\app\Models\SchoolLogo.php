<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class SchoolLogo extends Model
{
    use HasFactory;

    protected $fillable = [
        'filename',
        'original_name',
        'file_path',
        'mime_type',
        'file_size',
        'is_active',
        'description'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'file_size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the active logo
     */
    public static function getActiveLogo()
    {
        return self::where('is_active', true)
                   ->orderBy('created_at', 'desc')
                   ->first();
    }

    /**
     * Get the latest logo (most recent upload)
     */
    public static function getLatestLogo()
    {
        return self::orderBy('created_at', 'desc')->first();
    }

    /**
     * Set this logo as active and deactivate others
     */
    public function setAsActive()
    {
        // Deactivate all other logos
        self::where('id', '!=', $this->id)->update(['is_active' => false]);
        
        // Activate this logo
        $this->update(['is_active' => true]);
        
        return $this;
    }

    /**
     * Get the full URL of the logo
     */
    public function getUrlAttribute()
    {
        if (Storage::disk('public')->exists($this->file_path)) {
            return Storage::disk('public')->url($this->file_path);
        }
        
        return null;
    }

    /**
     * Get formatted file size
     */
    public function getFormattedSizeAttribute()
    {
        $bytes = $this->file_size;
        
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Delete logo file when model is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($logo) {
            // Delete the actual file
            if (Storage::disk('public')->exists($logo->file_path)) {
                Storage::disk('public')->delete($logo->file_path);
            }
        });
    }
}
