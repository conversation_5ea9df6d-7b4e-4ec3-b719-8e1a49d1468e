import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../../hooks/useAdminAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import { Button, Card, Input, Textarea, Select, Modal } from '../../components/ui';
import { FadeIn, StaggerContainer, StaggerItem, HoverScale } from '../../components/ui/AnimatedComponents';
import { api } from '../../services/api';

// Validation schema
const gallerySchema = yup.object().shape({
  title: yup
    .string()
    .required('Judul harus diisi')
    .min(3, 'Judul minimal 3 karakter')
    .max(100, 'Judul maksimal 100 karakter'),
  description: yup
    .string()
    .required('Deskripsi harus diisi')
    .min(10, 'Deskripsi minimal 10 karakter')
    .max(500, 'Deskripsi maksimal 500 karakter'),
  category: yup
    .string()
    .required('Kategori harus dipilih')
    .oneOf(['Kegiatan Rutin', 'Kegiatan Khusus', 'Prestasi', 'Fasilitas', 'Ekstrakurikuler'], 'Kategori tidak valid'),
  featured: yup.boolean(),
  carousel_pinned: yup.boolean()
});

const GalleryManagement = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading: authLoading, redirectToLogin, user } = useAdminAuth();
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 5,
    total: 0
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      redirectToLogin();
    }
  }, [authLoading, isAuthenticated, redirectToLogin]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Bulk actions states
  const [selectedImageIds, setSelectedImageIds] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [fileError, setFileError] = useState('');
  
  // Image loading optimization
  const [loadedImages, setLoadedImages] = useState(new Set());
  const [loadingImages, setLoadingImages] = useState(false);

  // Cleanup preview URLs on unmount
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // React Hook Form
  const {
    register,
    handleSubmit: onSubmit,
    formState: { errors: formErrors, isSubmitting },
    reset,
    setValue,
    
  } = useForm({
    resolver: yupResolver(gallerySchema),
    defaultValues: {
      title: '',
      description: '',
      image_url: '',
      category: '',
      tags: '[]',
      uploaded_by: '',
      is_active: 1,
      featured: false,
      carousel_pinned: false,
      sort_order: 0
    }
  });

  const categories = [
    'Kegiatan Rutin',
    'Kegiatan Khusus',
    'Prestasi',
    'Fasilitas',
    'Ekstrakurikuler'
  ];

  // Image loading handlers
  const handleImageLoad = (imageId) => {
    const newLoadedImages = new Set(loadedImages);
    newLoadedImages.add(imageId);
    setLoadedImages(newLoadedImages);
    
    // Check if all images are loaded
    if (newLoadedImages.size === images.length) {
      setLoadingImages(false);
    }
  };

  const isImageLoaded = (imageId) => {
    return loadedImages.has(imageId);
  };

  // Fetch gallery data from API with optimization
  const fetchGallery = React.useCallback(async (page = 1, filters = {}) => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      setError(null);
      // Reset image loading state
      setLoadedImages(new Set());
      setLoadingImages(true);

      // Build query parameters for optimization
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: '12', // Increased items per page for better UX
        ...filters,
        timestamp: new Date().getTime() // Cache busting
      });

      const response = await api.get(`/gallery/admin/all?${params.toString()}`);
      console.log('Gallery API Response:', response.data);

      if (response.data.success) {
        // Batch state updates
        const updates = {
          images: response.data.data || [],
          pagination: response.data.pagination || {
            current_page: 1,
            last_page: 1,
            per_page: 12,
            total: 0
          }
        };

        // Validate data
        if (!Array.isArray(updates.images)) {
          console.error('Invalid gallery data format:', updates.images);
          throw new Error('Format data galeri tidak valid');
        }

        console.log('Setting gallery images:', updates.images.length, 'items');
        
        // Use a single render cycle for multiple state updates
        setImages(updates.images);
        setPagination(updates.pagination);
        
        if (updates.images.length === 0) {
          console.log('No gallery images returned from API');
        }
      } else {
        console.error('API request failed:', response.data.error);
        throw new Error(response.data.error || 'Gagal memuat data galeri');
      }
    } catch (err) {
      console.error('Error fetching gallery:', err);
      const errorMessage = err.message?.includes('Authentication required') || err.message?.includes('401')
        ? 'Sesi login Anda telah berakhir. Silakan login kembali.'
        : 'Terjadi kesalahan saat memuat data. Silakan coba lagi.';
      
      setError(errorMessage);
      
      if (errorMessage.includes('Sesi login')) {
        setTimeout(() => navigate('/admin/login'), 2000);
      }
      
      // Keep old data if error occurs instead of clearing
      // setImages([]);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, navigate]);

  // Memoize fetchGallery to prevent unnecessary re-renders
  const memoizedFetchGallery = React.useCallback(fetchGallery, [isAuthenticated, navigate]);

  // Load data on component mount and category changes
  useEffect(() => {
    let mounted = true;

    const loadData = async () => {
      if (!mounted) return;
      
      // Don't show loading state for quick refreshes
      const loadingTimeout = setTimeout(() => {
        if (mounted) setLoading(true);
      }, 500);

      await memoizedFetchGallery(1, {
        category: selectedCategory === 'all' ? undefined : selectedCategory
      });

      clearTimeout(loadingTimeout);
      if (mounted) setLoading(false);
    };

    loadData();

    // Cleanup function
    return () => {
      mounted = false;
    };
  }, [selectedCategory, memoizedFetchGallery]);

  const resetForm = () => {
    // Cleanup preview URL if exists
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl('');
    }
    
    // Reset selected file
    setSelectedFile(null);
    
    // Reset form values
    reset({
      title: '',
      description: '',
      image_url: '',
      category: '',
      tags: '[]',
      uploaded_by: user?.id || 1,
      is_active: 1,
      featured: false,
      carousel_pinned: false,
      sort_order: 0
    });
    setSelectedFile(null);
    setPreviewUrl('');
    setFileError('');
  };

  const openModal = (mode, image = null) => {
    setModalMode(mode);
    setSelectedImage(image);
    setFileError('');

    if (mode === 'edit' && image) {
      // Set form values for editing
      setValue('title', image.title || '');
      setValue('description', image.description || '');
      setValue('category', image.category || '');
      setValue('featured', image.featured || false);
      setValue('carousel_pinned', image.carousel_pinned || false);
      
      // Set preview for existing image
      if (image.image_url) {
        setPreviewUrl(image.image_url);
      }
      
      setSelectedFile(null); // Reset selected file since we're editing
      
      console.log('Opening edit modal with data:', {
        id: image.id,
        title: image.title,
        category: image.category,
        image_url: image.image_url
      });
    } else if (mode === 'create') {
      resetForm();
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
    
    // Cleanup preview URL
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl('');
    }
    
    resetForm();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validasi tipe file
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setFileError('Tipe file tidak didukung. Gunakan JPG, PNG, GIF, atau WebP');
        return;
      }

      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Validasi ukuran file (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        setFileError('Ukuran file terlalu besar. Maksimal 5MB');
        return;
      }

      setSelectedFile(file);
      setFileError(''); // Clear error

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Form submit handler with react-hook-form
  const handleFormSubmit = async (data) => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);

      if (modalMode === 'create') {
        // Validasi file
        if (!selectedFile) {
          setFileError('File gambar harus dipilih');
          return;
        }

        // Create FormData for file upload
        if (!selectedFile && modalMode === 'create') {
          setError('Silakan pilih gambar terlebih dahulu');
          return;
        }

          const formData = new FormData();
          formData.append('title', data.title);
          formData.append('description', data.description || '');
          formData.append('category', data.category);
          formData.append('uploaded_by', user?.id?.toString() || '1'); 
          formData.append('is_active', '1');
          formData.append('featured', data.featured ? '1' : '0');
          formData.append('carousel_pinned', data.carousel_pinned ? '1' : '0');
          formData.append('_method', 'POST'); // Add method override
          
          if (selectedFile) {
            formData.append('image', selectedFile);
          }

          const response = await api.upload('/gallery', formData, {
            headers: {
              'Accept': 'application/json'
            }
          });
          
          if (response.data && (response.data.success || response.data.status === 'success')) {
            await fetchGallery(pagination.current_page, { category: selectedCategory });
            closeModal();
            // Reset form and selected file
            reset();
            setSelectedFile(null);
            setPreviewUrl(null);
          } else {
            setError(response.data?.message || 'Gagal menyimpan gambar');
          }
      } else if (modalMode === 'edit') {
        // Buat FormData untuk edit
        const formData = new FormData();
        
        // Tambahkan field yang diperlukan
        formData.append('title', data.title);
        formData.append('description', data.description);
        formData.append('category', data.category);
        formData.append('featured', data.featured ? '1' : '0');
        formData.append('carousel_pinned', data.carousel_pinned ? '1' : '0');
        formData.append('is_active', '1');
        formData.append('sort_order', '0');
        formData.append('_method', 'PUT'); // Laravel method spoofing

        // Tambahkan gambar baru jika ada
        if (selectedFile) {
          formData.append('image', selectedFile);
        }
        
        console.log('Updating gallery:', {
          id: selectedImage.id,
          title: data.title,
          category: data.category,
          hasNewImage: !!selectedFile
        });

        try {
          const response = await api.upload(`/gallery/${selectedImage.id}`, formData);
          
          if (response.data.success) {
            showToast('Gambar berhasil diperbarui!', 'success');
            await fetchGallery(pagination.current_page, { category: selectedCategory });
            closeModal();
          } else {
            throw new Error(response.data.message || 'Gagal mengupdate gambar');
          }
        } catch (error) {
          console.error('Error updating gallery:', error);
          setError(error.message || 'Gagal mengupdate gambar');
          showToast('Gagal mengupdate gambar: ' + error.message, 'error');
        }
      }
    } catch (err) {
      console.error('Error saving gallery:', err);
      if (err.message.includes('Authentication required') || err.message.includes('401')) {
        setError('Sesi login Anda telah berakhir. Silakan login kembali.');
        setTimeout(() => {
          navigate('/admin/login');
        }, 2000);
      } else if (err.message.includes('400')) {
        setError('Data yang dikirim tidak valid. Periksa kembali form Anda.');
      } else if (err.message.includes('500')) {
        setError('Terjadi kesalahan server. Pastikan Anda sudah login sebagai admin.');
      } else {
        setError('Terjadi kesalahan saat menyimpan data');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      const response = await api.delete(`/gallery/${selectedImage.id}`);
      if (response.data.success) {
        await fetchGallery(pagination.current_page, { category: selectedCategory }); // Refresh data with current page
        closeModal();
      } else {
        setError('Gagal menghapus gambar');
      }
    } catch (err) {
      console.error('Error deleting gallery:', err);
      setError('Terjadi kesalahan saat menghapus data');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleCarouselPin = async (imageId) => {
    try {
      const response = await api.post(`/gallery/${imageId}/toggle-carousel-pin`);
      if (response.data.success) {
        await fetchGallery(pagination.current_page, { category: selectedCategory }); // Refresh data with current page

        // Show success message with better UX
        const isPinned = response.data.data.carousel_pinned;
        const message = isPinned
          ? `✅ Gambar berhasil di-pin ke carousel! Sekarang akan muncul di halaman utama.`
          : `📌 Gambar berhasil di-unpin dari carousel.`;

        // Create toast notification instead of alert
        showToast(message, 'success');

        // Trigger carousel refresh di Home page (jika ada)
        if (window.refreshCarousel) {
          window.refreshCarousel();
        }
      } else {
        setError('Gagal mengubah status pin carousel');
      }
    } catch (err) {
      console.error('Error toggling carousel pin:', err);
      setError('Terjadi kesalahan saat mengubah status pin');
    }
  };

  // Toast notification function
  const showToast = (message, type = 'info') => {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white font-medium transform transition-all duration-300 ${
      type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    toast.textContent = message;

    // Add to DOM
    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (document.body.contains(toast)) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, 3000);
  };

  // Auto reload when no data is found
  useEffect(() => {
    let reloadTimer;
    if (images.length === 0 && !loading) {
      reloadTimer = setTimeout(() => {
        console.log('Auto-reloading data...');
        fetchGallery(1, { category: selectedCategory });
      }, 5000); // Reload setiap 5 detik jika tidak ada data
    }
    return () => {
      if (reloadTimer) clearTimeout(reloadTimer);
    };
  }, [images.length, loading, selectedCategory, fetchGallery]);

  // Memoize filtered images to prevent unnecessary recalculation
  const filteredImages = React.useMemo(() => {
    if (selectedCategory === 'all') return images;
    return images.filter(img => img.category === selectedCategory);
  }, [images, selectedCategory]);

  // Loading state component with load progress
  const LoadingState = () => {
    const loadingMessage = loadingImages ? `Memuat gambar (${loadedImages.size} dari ${images.length})...` : 'Memuat galeri...';
    return (
      <div className="w-full min-h-[400px] flex flex-col items-center justify-center">
        <div className="relative w-20 h-20">
          <div className="absolute top-0 left-0 w-full h-full border-4 border-blue-200 rounded-full animate-ping"></div>
          <div className="absolute top-0 left-0 w-full h-full border-4 border-blue-500 rounded-full animate-pulse"></div>
        </div>
        <p className="mt-4 text-gray-600">{loadingMessage}</p>
        {loadingImages && (
          <div className="w-64 h-2 bg-gray-200 rounded-full mt-4">
            <div 
              className="h-full bg-blue-500 rounded-full transition-all duration-300"
              style={{ width: `${(loadedImages.size / images.length) * 100}%` }}
            />
          </div>
        )}
      </div>
    );
  };

  // Bulk actions functions
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedImageIds([]);
      setSelectAll(false);
    } else {
      setSelectedImageIds(filteredImages.map(item => item.id));
      setSelectAll(true);
    }
  };

  const handleSelectImage = (imageId) => {
    if (selectedImageIds.includes(imageId)) {
      const newSelected = selectedImageIds.filter(id => id !== imageId);
      setSelectedImageIds(newSelected);
      setSelectAll(false);
    } else {
      const newSelected = [...selectedImageIds, imageId];
      setSelectedImageIds(newSelected);
      setSelectAll(newSelected.length === filteredImages.length);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedImageIds.length === 0) return;

    if (!confirm(`Apakah Anda yakin ingin menghapus ${selectedImageIds.length} gambar yang dipilih?`)) {
      return;
    }

    try {
      const deletePromises = selectedImageIds.map(id =>
        api.delete(`/gallery/${id}`)
      );

      await Promise.all(deletePromises);
      await fetchGallery(pagination.current_page, { category: selectedCategory }); // Refresh data with current page
      setSelectedImageIds([]);
      setSelectAll(false);
      alert(`${selectedImageIds.length} gambar berhasil dihapus!`);
    } catch (err) {
      console.error('Error bulk deleting images:', err);
      alert('Terjadi kesalahan saat menghapus gambar: ' + err.message);
    }
  };

  // Reset selection and image loading state when category changes
  useEffect(() => {
    setSelectedImageIds([]);
    setSelectAll(false);
    setLoadedImages(new Set());
    setLoadingImages(true);
  }, [selectedCategory]);

  // Show loading while checking auth or if not authenticated
  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {authLoading ? 'Checking authentication...' : 'Redirecting to login...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        {/* Header */}
        <FadeIn direction="down" className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Kelola Galeri</h1>
            <p className="text-gray-600 mt-2">
              Kelola semua gambar dan foto kegiatan sekolah
              {pagination.total > 0 && (
                <span className="text-blue-600 font-medium">
                  ({pagination.total} total, {pagination.per_page} per halaman)
                </span>
              )}
            </p>
          </div>
          <Button
            onClick={() => openModal('create')}
            variant="primary"
            size="lg"
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            }
          >
            Upload Gambar
          </Button>
        </FadeIn>

        {/* Error Alert */}
        {error && (
          <FadeIn className="mb-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Terjadi Kesalahan</h3>
                  <p className="text-sm text-red-700 mt-1">{error}</p>
                  {error.includes('login') && (
                    <div className="mt-3">
                      <Button
                        onClick={() => navigate('/admin/login')}
                        variant="outline"
                        size="sm"
                        className="border-red-300 text-red-700 hover:bg-red-50"
                      >
                        Login Sekarang
                      </Button>
                    </div>
                  )}
                </div>
                <div className="ml-auto">
                  <button
                    onClick={() => setError(null)}
                    className="text-red-400 hover:text-red-600"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </FadeIn>
        )}

        {/* Filter */}
        <FadeIn delay={0.2} className="mb-6">
          <StaggerContainer className="flex flex-wrap gap-2">
            <StaggerItem>
              <Button
                onClick={() => setSelectedCategory('all')}
                variant={selectedCategory === 'all' ? 'primary' : 'outline'}
                size="sm"
              >
                Semua ({images.length})
              </Button>
            </StaggerItem>
            {categories.map((category) => {
              const count = images.filter(img => img.category === category).length;
              return (
                <StaggerItem key={category}>
                  <Button
                    onClick={() => setSelectedCategory(category)}
                    variant={selectedCategory === category ? 'primary' : 'outline'}
                    size="sm"
                  >
                    {category} ({count})
                  </Button>
                </StaggerItem>
              );
            })}
          </StaggerContainer>
        </FadeIn>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-red-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Bulk Actions */}
        {selectedImageIds.length > 0 && (
          <FadeIn delay={0.3} className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-yellow-800 font-medium">
                  {selectedImageIds.length} gambar dipilih
                </span>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleBulkDelete}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition duration-300 flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  <span>Hapus {selectedImageIds.length} Item</span>
                </button>
                <button
                  onClick={() => {
                    setSelectedImageIds([]);
                    setSelectAll(false);
                  }}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg font-medium transition duration-300"
                >
                  Batal
                </button>
              </div>
            </div>
          </FadeIn>
        )}

        {/* Gallery Table */}
        <FadeIn delay={0.4} className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {/* Checkbox Column */}
                  <th className="px-2 sm:px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8 sm:w-12">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gambar
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Kategori
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Carousel
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Upload By
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tanggal
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  // Loading skeleton for 5 items per page
                  Array.from({ length: 5 }).map((_, index) => (
                    <tr key={`skeleton-${index}`} className="animate-pulse">
                      <td className="px-2 sm:px-3 py-4">
                        <div className="h-4 w-4 bg-gray-200 rounded"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-16 w-16 bg-gray-200 rounded"></div>
                          <div className="ml-4">
                            <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                            <div className="h-3 bg-gray-200 rounded w-24"></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="h-4 bg-gray-200 rounded w-16"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="h-4 bg-gray-200 rounded w-16"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="h-4 bg-gray-200 rounded w-24"></div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex space-x-1">
                          <div className="h-8 w-8 bg-gray-200 rounded"></div>
                          <div className="h-8 w-8 bg-gray-200 rounded"></div>
                          <div className="h-8 w-8 bg-gray-200 rounded"></div>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : filteredImages.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">Data tidak ditemukan</h3>
                        <div className="animate-pulse">
                          <div className="flex items-center justify-center mb-4">
                            <svg className="animate-spin h-5 w-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <p className="text-gray-500">Memuat ulang data secara otomatis...</p>
                          </div>
                        </div>
                        <div className="mt-4">
                          <Button
                            onClick={() => openModal('create')}
                            variant="primary"
                            size="md"
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                            }
                          >
                            Upload Gambar Baru
                          </Button>
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredImages.map((image) => {
                    const isSelected = selectedImageIds.includes(image.id);
                    return (
                    <tr
                      key={image.id}
                      className={`transition-colors duration-200 ${
                        isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'
                      }`}
                    >
                      {/* Checkbox Column */}
                      <td className="px-2 sm:px-3 py-4">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleSelectImage(image.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4">
                      <div className="flex items-center">
                        <HoverScale className="flex-shrink-0 h-16 w-16 relative">
                          {!isImageLoaded(image.id) && (
                            <div className="absolute inset-0 bg-gray-100 animate-pulse rounded-lg z-0"></div>
                          )}
                          <img
                            src={image.image_url || '/placeholder-image.jpg'}
                            alt={image.title}
                            className={`h-16 w-16 rounded-lg object-cover transition-opacity duration-300 relative z-10 ${isImageLoaded(image.id) ? 'opacity-100' : 'opacity-0'}`}
                            onLoad={() => handleImageLoad(image.id)}
                            onError={(e) => {
                              e.target.src = '/placeholder-image.jpg';
                              handleImageLoad(image.id);
                            }}
                          />
                        </HoverScale>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 line-clamp-2">
                            {image.title}
                          </div>
                          <div className="text-sm text-gray-500 mt-1 line-clamp-1">
                            {image.description}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                        {typeof image.category === 'object' ? image.category?.name : image.category || 'Umum'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {image.featured || image.is_featured ? (
                        <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                          Featured
                        </span>
                      ) : (
                        <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                          Normal
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {image.carousel_pinned ? (
                          <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                            Pinned
                          </span>
                        ) : (
                          <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                            Not Pinned
                          </span>
                        )}
                        <Button
                          onClick={() => handleToggleCarouselPin(image.id)}
                          variant="ghost"
                          size="sm"
                          className={`${image.carousel_pinned ? 'text-red-600 hover:text-red-900 hover:bg-red-50' : 'text-green-600 hover:text-green-900 hover:bg-green-50'}`}
                          icon={
                            image.carousel_pinned ? (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            ) : (
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                              </svg>
                            )
                          }
                          title={image.carousel_pinned ? 'Unpin dari Carousel' : 'Pin ke Carousel'}
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {typeof image.uploadedBy === 'object' ? image.uploadedBy?.name : image.uploadedBy ||
                       typeof image.uploaded_by === 'object' ? image.uploaded_by?.name : image.uploaded_by || 'Admin'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {image.created_at ? new Date(image.created_at).toLocaleDateString('id-ID') :
                       image.date ? new Date(image.date).toLocaleDateString('id-ID') : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-1">
                        <Button
                          onClick={() => openModal('view', image)}
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-900 hover:bg-blue-50"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          }
                        />
                        <Button
                          onClick={() => openModal('edit', image)}
                          variant="ghost"
                          size="sm"
                          className="text-green-600 hover:text-green-900 hover:bg-green-50"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          }
                        />
                        <Button
                          onClick={() => openModal('delete', image)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-900 hover:bg-red-50"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          }
                        />
                      </div>
                    </td>
                  </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination - Shows when more than 5 items */}
          {pagination.last_page > 1 && (
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <Button
                  onClick={() => fetchGallery(pagination.current_page - 1, { category: selectedCategory })}
                  disabled={pagination.current_page <= 1}
                  variant="outline"
                  size="sm"
                >
                  ← Previous
                </Button>
                <span className="text-sm text-gray-700">
                  Page {pagination.current_page} of {pagination.last_page}
                </span>
                <Button
                  onClick={() => fetchGallery(pagination.current_page + 1, { category: selectedCategory })}
                  disabled={pagination.current_page >= pagination.last_page}
                  variant="outline"
                  size="sm"
                >
                  Next →
                </Button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{pagination.from || 1}</span> to{' '}
                    <span className="font-medium">{pagination.to || pagination.per_page}</span> of{' '}
                    <span className="font-medium">{pagination.total}</span> results
                    <span className="text-gray-500 ml-2">(5 items per page)</span>
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <Button
                      onClick={() => fetchGallery(pagination.current_page - 1, { category: selectedCategory })}
                      disabled={pagination.current_page <= 1}
                      variant="outline"
                      size="sm"
                      className="relative inline-flex items-center px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      ← Previous
                    </Button>

                    {/* Page Numbers */}
                    {Array.from({ length: pagination.last_page }, (_, i) => i + 1).map((pageNum) => (
                      <Button
                        key={pageNum}
                        onClick={() => fetchGallery(pageNum, { category: selectedCategory })}
                        variant={pageNum === pagination.current_page ? "primary" : "outline"}
                        size="sm"
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          pageNum === pagination.current_page
                            ? 'z-10 bg-blue-600 border-blue-600 text-white'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </Button>
                    ))}

                    <Button
                      onClick={() => fetchGallery(pagination.current_page + 1, { category: selectedCategory })}
                      disabled={pagination.current_page >= pagination.last_page}
                      variant="outline"
                      size="sm"
                      className="relative inline-flex items-center px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      Next →
                    </Button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </FadeIn>

        {/* Create/Edit Modal */}
        <Modal
          isOpen={isModalOpen && (modalMode === 'create' || modalMode === 'edit')}
          onClose={closeModal}
          title={modalMode === 'create' ? 'Upload Gambar Baru' : 'Edit Gambar'}
          size="lg"
        >
          <form onSubmit={onSubmit(handleFormSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Judul"
                placeholder="Masukkan judul gambar"
                required
                error={formErrors.title?.message}
                {...register('title')}
              />

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  Kategori <span className="text-red-500">*</span>
                </label>
                <select
                  id="category"
                  className={`w-full px-4 py-3 rounded-lg border focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    formErrors.category ? 'border-red-500' : 'border-gray-300'
                  }`}
                  {...register('category')}
                >
                  <option value="">Pilih Kategori</option>
                  {categories.map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                {formErrors.category && (
                  <p className="mt-1 text-sm text-red-500">{formErrors.category.message}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="image-upload" className="block text-sm font-medium text-gray-700 mb-2">
                Upload Gambar {modalMode === 'create' ? '*' : '(Opsional)'}
              </label>
              <div className="space-y-4">
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    fileError ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {/* Image Preview */}
                {(previewUrl || (selectedImage && !selectedFile)) && (
                  <div className="mt-4">
                    <div className="relative group">
                      <img
                        src={previewUrl || selectedImage?.image_url}
                        alt="Preview"
                        className="w-full max-h-[300px] object-contain rounded-lg border border-gray-200"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300 rounded-lg"></div>
                    </div>
                  </div>
                )}
                {fileError && (
                  <p className="text-red-600 text-sm mt-1 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {fileError}
                  </p>
                )}

                {/* Preview */}
                {previewUrl && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Preview:</label>
                    <div className="relative inline-block">
                      <img
                        src={previewUrl}
                        alt="Preview"
                        className="max-w-full h-32 object-cover rounded-lg border"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setPreviewUrl('');
                          setSelectedFile(null);
                          if (modalMode === 'edit') {
                            setPreviewUrl(selectedImage?.url || '');
                          }
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                )}

                <p className="text-sm text-gray-500">
                  Format yang didukung: JPG, PNG, GIF, WebP. Maksimal 5MB.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">


            </div>

            <Textarea
              label="Deskripsi"
              placeholder="Deskripsi gambar"
              rows={4}
              required
              error={formErrors.description?.message}
              {...register('description')}
            />

            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  {...register('featured')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
                  Jadikan gambar featured
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="carousel_pinned"
                  {...register('carousel_pinned')}
                  className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                />
                <label htmlFor="carousel_pinned" className="ml-2 block text-sm text-gray-900">
                  Pin ke Carousel (tampil di halaman utama)
                </label>
              </div>
            </div>

            <Modal.Footer>
              <Button
                type="button"
                onClick={closeModal}
                variant="outline"
                disabled={isSubmitting}
              >
                Batal
              </Button>
              <Button
                type="submit"
                variant="primary"
                loading={isSubmitting}
                disabled={isSubmitting}
              >
                {modalMode === 'create' ? 'Upload' : 'Update'}
              </Button>
            </Modal.Footer>
          </form>
        </Modal>

        {/* View Modal - Enhanced Card Design */}
        <Modal
          isOpen={isModalOpen && modalMode === 'view'}
          onClose={closeModal}
          title=""
          size="xl"
          className="p-0"
        >
          {selectedImage && (
            <div className="bg-white rounded-lg overflow-hidden">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold text-white">Detail Gambar</h2>
                  <button
                    onClick={closeModal}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              <div className="p-6">
                {/* Image Card */}
                <div className="bg-gray-50 rounded-xl p-4 mb-6">
                  <div className="relative group">
                    <img
                      src={selectedImage.url || selectedImage.image_url || selectedImage.path || '/images/gallery/gallery_1_1753243029.jpg'}
                      alt={selectedImage.title}
                      className="w-full h-auto rounded-lg shadow-lg transition-transform duration-300 group-hover:scale-105"
                      style={{ maxHeight: '500px', objectFit: 'contain' }}
                      onError={(e) => {
                        e.target.src = '/images/gallery/gallery_1_1753243029.jpg';
                      }}
                    />

                    {/* Image overlay with info */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 rounded-lg flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Cards */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Info Card */}
                  <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">Informasi Gambar</h3>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Judul</label>
                        <p className="text-gray-900 font-medium">{selectedImage.title}</p>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-gray-500">Kategori</label>
                          <div className="flex items-center mt-1">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {selectedImage.category}
                            </span>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-gray-500">Status</label>
                          <div className="flex items-center mt-1 space-x-2">
                            {selectedImage.featured && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                ⭐ Featured
                              </span>
                            )}
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              ✓ Aktif
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Details Card */}
                  <div className="bg-white border border-gray-200 rounded-xl p-5 shadow-sm">
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900">Detail</h3>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-500">Deskripsi</label>
                        <p className="text-gray-700 mt-1 leading-relaxed">
                          {selectedImage.description || 'Tidak ada deskripsi'}
                        </p>
                      </div>

                      <div className="grid grid-cols-1 gap-3 pt-3 border-t border-gray-100">
                        <div className="flex items-center text-sm text-gray-600">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          <span>Diupload oleh: {selectedImage.uploadedBy || 'Admin'}</span>
                        </div>

                        <div className="flex items-center text-sm text-gray-600">
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a1 1 0 011 1v9a2 2 0 01-2 2H5a2 2 0 01-2-2V8a1 1 0 011-1h3z" />
                          </svg>
                          <span>Tanggal: {selectedImage.created_at ? new Date(selectedImage.created_at).toLocaleDateString('id-ID', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Tidak diketahui'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                  <Button
                    onClick={() => {
                      closeModal();
                      openModal('edit', selectedImage);
                    }}
                    variant="outline"
                    className="flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    <span>Edit</span>
                  </Button>

                  <Button
                    onClick={closeModal}
                    variant="primary"
                    className="flex items-center space-x-2"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Selesai</span>
                  </Button>
                </div>
              </div>
            </div>
          )}
        </Modal>

        {/* Delete Modal */}
        <Modal
          isOpen={isModalOpen && modalMode === 'delete'}
          onClose={closeModal}
          title="Hapus Gambar"
          size="sm"
        >
          {selectedImage && (
            <div>
              <div className="mb-4">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                  <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-600 text-center">
                  Apakah Anda yakin ingin menghapus gambar ini? Tindakan ini tidak dapat dibatalkan.
                </p>
                <p className="text-sm font-medium text-gray-900 text-center mt-2">
                  "{selectedImage.title}"
                </p>
              </div>
              
              <Modal.Footer>
                <Button
                  onClick={closeModal}
                  variant="outline"
                >
                  Batal
                </Button>
                <Button
                  onClick={handleDelete}
                  variant="danger"
                  loading={loading}
                  disabled={loading}
                >
                  Hapus
                </Button>
              </Modal.Footer>
            </div>
          )}
        </Modal>
      </div>
    </AdminLayout>
  );
};

export default GalleryManagement;
