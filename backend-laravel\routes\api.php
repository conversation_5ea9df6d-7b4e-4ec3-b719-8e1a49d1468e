<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\GalleryController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\SchoolLogoController;
use App\Http\Controllers\LogoController;
use App\Http\Controllers\WebsiteSettingsController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check
Route::get('/health', function () {
    return response()->json([
        'status' => 'OK',
        'message' => 'School Management API is running',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0'
    ]);
});

// Public routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// News routes (public)
Route::prefix('news')->group(function () {
    Route::get('/', [NewsController::class, 'index']);
    Route::get('/{id}', [NewsController::class, 'show']);
});

// Gallery routes (public)
Route::prefix('gallery')->group(function () {
    Route::get('/', [GalleryController::class, 'index']);
    Route::get('/carousel', [GalleryController::class, 'getCarouselImages']);
    Route::get('/admin/all', [GalleryController::class, 'adminIndex']);
    Route::get('/{id}', [GalleryController::class, 'show']);

    // Temporary public upload for testing (remove auth requirement)
    Route::post('/', [GalleryController::class, 'store']);
});

// Contact routes (public)
Route::prefix('contacts')->group(function () {
    Route::get('/', [ContactController::class, 'index']);
    Route::post('/', [ContactController::class, 'store']);
});

// Settings routes (public)
Route::get('/settings', [SettingsController::class, 'index']);

// Public dashboard stats
Route::get('/dashboard/public-stats', [DashboardController::class, 'getPublicStats']);

// Protected routes (require authentication)
Route::middleware('auth:sanctum')->group(function () {
    
    // Auth routes
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/me', [AuthController::class, 'me']);
    });

    // Dashboard routes
    Route::prefix('dashboard')->group(function () {
        Route::get('/stats', [DashboardController::class, 'getStats']);
        Route::get('/recent-news', [DashboardController::class, 'getRecentNews']);
        Route::get('/recent-activities', [DashboardController::class, 'getRecentActivities']);
    });

    // Admin only routes
    Route::middleware('admin')->group(function () {

        // User management
        Route::post('/auth/register', [AuthController::class, 'register']);
        
        // News management
        Route::prefix('news')->group(function () {
            Route::get('/admin/all', [NewsController::class, 'adminIndex']);
            Route::post('/', [NewsController::class, 'store']);
            Route::put('/{id}', [NewsController::class, 'update']);
            Route::delete('/{id}', [NewsController::class, 'destroy']);
        });

        // Gallery management (protected routes)
        Route::prefix('gallery')->group(function () {
            Route::put('/{id}', [GalleryController::class, 'update']);
            Route::delete('/{id}', [GalleryController::class, 'destroy']);
            Route::post('/{id}/toggle-carousel-pin', [GalleryController::class, 'toggleCarouselPin']);
        });

        // Contact management
        Route::prefix('contacts')->group(function () {
            Route::get('/admin/all', [ContactController::class, 'adminIndex']);
            Route::get('/{id}', [ContactController::class, 'show']);
            Route::put('/{id}', [ContactController::class, 'update']);
            Route::delete('/{id}', [ContactController::class, 'destroy']);
            Route::post('/bulk-update', [ContactController::class, 'bulkUpdate']);
            Route::post('/bulk-delete', [ContactController::class, 'bulkDestroy']);
        });

        // User management
        Route::prefix('users')->group(function () {
            Route::get('/', [UserController::class, 'index']);
            Route::get('/{id}', [UserController::class, 'show']);
            Route::post('/', [UserController::class, 'store']);
            Route::put('/{id}', [UserController::class, 'update']);
            Route::delete('/{id}', [UserController::class, 'destroy']);
            Route::patch('/{id}/toggle-status', [UserController::class, 'toggleStatus']);
        });

        // Settings management
        Route::prefix('settings')->group(function () {
            Route::get('/admin/all', [SettingsController::class, 'adminIndex']);
            Route::put('/', [SettingsController::class, 'update']);
            Route::post('/', [SettingsController::class, 'store']);
            Route::post('/logo', [SettingsController::class, 'uploadLogo']);
            Route::get('/{key}', [SettingsController::class, 'show']);
            Route::delete('/{key}', [SettingsController::class, 'destroy']);
            
            // Website Settings Routes
            Route::get('/website/meta', [WebsiteSettingsController::class, 'getWebsiteSettings']);
            Route::post('/website/update', [WebsiteSettingsController::class, 'updateWebsiteSettings']);
        });
    });
});

// Logo Routes (Simple File-based Version)
Route::prefix('logo')->group(function () {
    Route::post('/upload', [\App\Http\Controllers\SimpleLogoController::class, 'upload']);
    Route::get('/current', [\App\Http\Controllers\SimpleLogoController::class, 'getCurrent']);
});

// School Logo Routes (Old Version - Keep for compatibility)
Route::prefix('logos')->group(function () {
    // Public routes (no auth required)
    Route::get('/current', [SchoolLogoController::class, 'getCurrentLogo']);
    Route::get('/latest', [SchoolLogoController::class, 'getLatestLogo']);
    Route::get('/all', [SchoolLogoController::class, 'getAllLogos']);

    // Temporary public upload for testing (remove auth requirement)
    Route::post('/upload', [SchoolLogoController::class, 'uploadLogo']);

    // Protected routes (require authentication)
    Route::middleware('auth:sanctum')->group(function () {
        Route::delete('/{id}', [SchoolLogoController::class, 'deleteLogo']);
        Route::put('/{id}/activate', [SchoolLogoController::class, 'setActiveLogo']);
    });
});

// Fallback route
Route::fallback(function () {
    return response()->json([
        'error' => 'Endpoint not found',
        'message' => 'The requested API endpoint does not exist'
    ], 404);
});
