import { useEffect } from 'react';
import { useSettings } from '../contexts/SettingsContext';

export function DocumentHead() {
  const { schoolName, schoolLogo } = useSettings();

  useEffect(() => {
    // Update document title
    if (schoolName) {
      document.title = schoolName;
    }

    // Update favicon
    if (schoolLogo) {
      const favicon = document.querySelector('link[rel="icon"]');
      if (favicon) {
        favicon.href = schoolLogo;
      }
    }
  }, [schoolName, schoolLogo]);

  return null; // This is a utility component that doesn't render anything
}
