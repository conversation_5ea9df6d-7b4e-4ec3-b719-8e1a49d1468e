<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop existing role column
            $table->dropColumn('role');
        });

        // Add new role column with more options
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['admin', 'kepala_sekolah', 'guru', 'staff', 'user'])
                  ->default('user')
                  ->after('password');

            // Add additional fields for user management
            $table->string('phone')->nullable()->after('email');
            $table->text('bio')->nullable()->after('avatar');
            $table->string('position')->nullable()->after('bio'); // Jabatan
            $table->string('employee_id')->nullable()->after('position'); // NIP/NIK
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Remove new fields
            $table->dropColumn(['phone', 'bio', 'position', 'employee_id']);

            // Drop new role column
            $table->dropColumn('role');
        });

        // Restore original role column
        Schema::table('users', function (Blueprint $table) {
            $table->enum('role', ['admin', 'user'])->default('user')->after('password');
        });
    }
};
