<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class WebsiteSettingsController extends Controller
{
    /**
     * Get website settings (title, favicon, meta description)
     */
    public function getWebsiteSettings()
    {
        try {
            $settings = Setting::whereIn('key', ['website_title', 'website_favicon', 'meta_description'])->get();
            
            $formattedSettings = [
                'title' => optional($settings->where('key', 'website_title')->first())->value ?: 'SMA Negeri 1 Jakarta',
                'favicon' => optional($settings->where('key', 'website_favicon')->first())->value ?: '/logo/default-favicon.png',
                'description' => optional($settings->where('key', 'meta_description')->first())->value ?: 'Website resmi SMA Negeri 1 Jakarta'
            ];

            return response()->json([
                'success' => true,
                'data' => $formattedSettings
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Website settings error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Failed to retrieve website settings',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update website settings
     */
    public function updateWebsiteSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:500',
            'logo' => 'sometimes|file|mimes:jpeg,png,gif|max:2048',
            'favicon' => 'sometimes|file|mimes:ico,png|max:1024'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        try {
            DB::beginTransaction();

            // Update title
            Setting::updateOrCreate(
                ['key' => 'website_title'],
                [
                    'value' => $request->title,
                    'type' => 'string',
                    'category' => 'general',
                    'is_public' => true,
                    'updated_by' => auth()->id() ?? 1
                ]
            );

            // Update description
            Setting::updateOrCreate(
                ['key' => 'meta_description'],
                [
                    'value' => $request->description,
                    'type' => 'string',
                    'category' => 'general',
                    'is_public' => true,
                    'updated_by' => auth()->id() ?? 1
                ]
            );

            DB::commit();

            return $this->getWebsiteSettings();

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Website settings update error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to update website settings',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
