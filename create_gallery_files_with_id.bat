@echo off
echo Creating gallery files with ID format...

cd frontend\public\images\gallery

echo Current files:
dir /b

echo.
echo Creating files with ID format...

if exist gallery_1753243029.jpg (
    copy gallery_1753243029.jpg gallery_1_1753243029.jpg
    echo Created: gallery_1_1753243029.jpg
)

if exist gallery_1753243054.png (
    copy gallery_1753243054.png gallery_2_1753243054.png
    echo Created: gallery_2_1753243054.png
)

if exist gallery_1753243125.png (
    copy gallery_1753243125.png gallery_3_1753243125.png
    echo Created: gallery_3_1753243125.png
)

if exist gallery_1753243286.jpg (
    copy gallery_1753243286.jpg gallery_4_1753243286.jpg
    echo Created: gallery_4_1753243286.jpg
)

if exist gallery_1753321778.jpg (
    copy gallery_1753321778.jpg gallery_5_1753321778.jpg
    echo Created: gallery_5_1753321778.jpg
)

echo.
echo Files after creation:
dir /b

echo.
echo Done! Gallery files with ID format created successfully.
pause
