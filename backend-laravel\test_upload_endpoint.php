<?php

/**
 * Test script untuk endpoint upload logo
 * Run: php test_upload_endpoint.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use App\Http\Controllers\SchoolLogoController;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING LOGO UPLOAD ENDPOINT ===\n\n";

// Test 1: Check if controller exists
echo "1. Testing controller instantiation...\n";
try {
    $controller = new SchoolLogoController();
    echo "✅ SchoolLogoController instantiated successfully\n\n";
} catch (Exception $e) {
    echo "❌ Error instantiating controller: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Test getCurrentLogo method
echo "2. Testing getCurrentLogo method...\n";
try {
    $response = $controller->getCurrentLogo();
    $responseData = json_decode($response->getContent(), true);
    
    echo "✅ getCurrentLogo method works\n";
    echo "Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";
} catch (Exception $e) {
    echo "❌ Error in getCurrentLogo: " . $e->getMessage() . "\n\n";
}

// Test 3: Test getLatestLogo method
echo "3. Testing getLatestLogo method...\n";
try {
    $response = $controller->getLatestLogo();
    $responseData = json_decode($response->getContent(), true);
    
    echo "✅ getLatestLogo method works\n";
    echo "Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";
} catch (Exception $e) {
    echo "❌ Error in getLatestLogo: " . $e->getMessage() . "\n\n";
}

// Test 4: Test getAllLogos method
echo "4. Testing getAllLogos method...\n";
try {
    $response = $controller->getAllLogos();
    $responseData = json_decode($response->getContent(), true);
    
    echo "✅ getAllLogos method works\n";
    echo "Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";
} catch (Exception $e) {
    echo "❌ Error in getAllLogos: " . $e->getMessage() . "\n\n";
}

// Test 5: Create a fake uploaded file for testing upload
echo "5. Testing uploadLogo method with fake file...\n";
try {
    // Create a simple test image content (1x1 pixel PNG)
    $pngData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==');
    
    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_logo') . '.png';
    file_put_contents($tempFile, $pngData);
    
    // Create UploadedFile instance
    $uploadedFile = new UploadedFile(
        $tempFile,
        'test_logo.png',
        'image/png',
        null,
        true // test mode
    );
    
    // Create request with file
    $request = new Request();
    $request->files->set('logo', $uploadedFile);
    $request->request->set('description', 'Test logo upload');
    
    echo "📁 Test file created: {$tempFile}\n";
    echo "📊 File size: " . filesize($tempFile) . " bytes\n";
    
    // Test upload
    $response = $controller->uploadLogo($request);
    $responseData = json_decode($response->getContent(), true);
    
    if ($response->getStatusCode() === 200 && $responseData['success']) {
        echo "✅ Upload successful!\n";
        echo "📁 Saved as: " . $responseData['data']['filename'] . "\n";
        echo "🗄️ Database ID: " . $responseData['data']['id'] . "\n";
        echo "📊 File size: " . $responseData['data']['formatted_size'] . "\n";
        echo "🔗 URL: " . $responseData['data']['url'] . "\n\n";
        
        // Verify file exists in storage
        $filePath = $responseData['data']['file_path'] ?? null;
        if ($filePath && Storage::disk('public')->exists($filePath)) {
            echo "✅ File exists in storage: storage/app/public/{$filePath}\n";
        } else {
            echo "❌ File not found in storage\n";
        }
        
        // Verify database record
        $logoId = $responseData['data']['id'];
        $dbRecord = DB::table('school_logos')->where('id', $logoId)->first();
        if ($dbRecord) {
            echo "✅ Database record exists:\n";
            echo "  - ID: {$dbRecord->id}\n";
            echo "  - Filename: {$dbRecord->filename}\n";
            echo "  - Original Name: {$dbRecord->original_name}\n";
            echo "  - File Path: {$dbRecord->file_path}\n";
            echo "  - Is Active: {$dbRecord->is_active}\n\n";
        } else {
            echo "❌ Database record not found\n\n";
        }
        
    } else {
        echo "❌ Upload failed\n";
        echo "Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";
    }
    
    // Clean up temp file
    if (file_exists($tempFile)) {
        unlink($tempFile);
        echo "🧹 Temporary file cleaned up\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error in uploadLogo: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n\n";
}

// Test 6: Check storage directory permissions
echo "6. Checking storage directory...\n";
try {
    $logoStoragePath = storage_path('app/public/logos');
    
    if (is_dir($logoStoragePath)) {
        echo "✅ Storage directory exists: {$logoStoragePath}\n";
        
        if (is_writable($logoStoragePath)) {
            echo "✅ Storage directory is writable\n";
        } else {
            echo "❌ Storage directory is not writable\n";
        }
        
        // List files in directory
        $files = scandir($logoStoragePath);
        $logoFiles = array_filter($files, function($file) {
            return !in_array($file, ['.', '..']);
        });
        
        echo "📁 Files in storage directory: " . count($logoFiles) . "\n";
        foreach ($logoFiles as $file) {
            echo "  - {$file}\n";
        }
        
    } else {
        echo "❌ Storage directory does not exist: {$logoStoragePath}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking storage: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETED ===\n";
echo "Check the results above to see if logo upload is working correctly.\n\n";
