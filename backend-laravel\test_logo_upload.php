<?php

/**
 * Simple test script to test logo upload functionality
 * Run this script from command line: php test_logo_upload.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== LOGO UPLOAD TEST SCRIPT ===\n\n";

// Test 1: Database Connection
echo "1. Testing database connection...\n";
try {
    $result = DB::select('SELECT 1 as test');
    echo "✓ Database connection successful\n\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Check if school_logos table exists
echo "2. Checking school_logos table...\n";
try {
    $tableExists = DB::select("SHOW TABLES LIKE 'school_logos'");
    if (empty($tableExists)) {
        echo "✗ school_logos table does not exist\n";
        echo "Please run: php artisan migrate\n\n";
        exit(1);
    }
    echo "✓ school_logos table exists\n\n";
} catch (Exception $e) {
    echo "✗ Error checking table: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 3: Check table structure
echo "3. Checking table structure...\n";
try {
    $columns = DB::select("DESCRIBE school_logos");
    $columnNames = array_column($columns, 'Field');
    
    $expectedColumns = [
        'id', 'filename', 'original_name', 'file_path', 
        'mime_type', 'file_size', 'is_active', 'description', 
        'created_at', 'updated_at'
    ];
    
    $missingColumns = array_diff($expectedColumns, $columnNames);
    
    if (!empty($missingColumns)) {
        echo "✗ Missing columns: " . implode(', ', $missingColumns) . "\n\n";
        exit(1);
    }
    
    echo "✓ Table structure is correct\n";
    echo "Columns: " . implode(', ', $columnNames) . "\n\n";
} catch (Exception $e) {
    echo "✗ Error checking table structure: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 4: Insert test logo data
echo "4. Testing logo data insertion...\n";
try {
    // Clean up any existing test data
    DB::table('school_logos')->where('filename', 'LIKE', 'test_%')->delete();
    
    $testData = [
        'filename' => 'test_logo_' . Str::random(8) . '.jpg',
        'original_name' => 'test_logo.jpg',
        'file_path' => 'logos/test_logo.jpg',
        'mime_type' => 'image/jpeg',
        'file_size' => 1024,
        'is_active' => 1,
        'description' => 'Test logo for upload functionality',
        'created_at' => now(),
        'updated_at' => now()
    ];
    
    $logoId = DB::table('school_logos')->insertGetId($testData);
    
    if ($logoId) {
        echo "✓ Logo data inserted successfully (ID: {$logoId})\n";
        
        // Verify the data
        $insertedLogo = DB::table('school_logos')->where('id', $logoId)->first();
        if ($insertedLogo) {
            echo "✓ Logo data verified:\n";
            echo "  - Filename: {$insertedLogo->filename}\n";
            echo "  - Original Name: {$insertedLogo->original_name}\n";
            echo "  - File Size: {$insertedLogo->file_size} bytes\n";
            echo "  - Is Active: {$insertedLogo->is_active}\n";
            echo "  - Description: {$insertedLogo->description}\n\n";
        }
    } else {
        echo "✗ Failed to insert logo data\n\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "✗ Error inserting logo data: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 5: Test active logo functionality
echo "5. Testing active logo functionality...\n";
try {
    // Insert another logo
    $secondLogoId = DB::table('school_logos')->insertGetId([
        'filename' => 'test_logo_2_' . Str::random(8) . '.jpg',
        'original_name' => 'test_logo_2.jpg',
        'file_path' => 'logos/test_logo_2.jpg',
        'mime_type' => 'image/jpeg',
        'file_size' => 2048,
        'is_active' => 0,
        'description' => 'Second test logo',
        'created_at' => now(),
        'updated_at' => now()
    ]);
    
    // Deactivate all logos
    DB::table('school_logos')->update(['is_active' => 0]);
    
    // Activate the second logo
    DB::table('school_logos')->where('id', $secondLogoId)->update(['is_active' => 1]);
    
    // Get active logo
    $activeLogo = DB::table('school_logos')
                   ->where('is_active', 1)
                   ->orderBy('created_at', 'desc')
                   ->first();
    
    if ($activeLogo && $activeLogo->id == $secondLogoId) {
        echo "✓ Active logo functionality working correctly\n";
        echo "  - Active logo: {$activeLogo->filename}\n\n";
    } else {
        echo "✗ Active logo functionality not working\n\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "✗ Error testing active logo: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 6: Test latest logo functionality
echo "6. Testing latest logo functionality...\n";
try {
    $latestLogo = DB::table('school_logos')
                   ->orderBy('created_at', 'desc')
                   ->first();
    
    if ($latestLogo) {
        echo "✓ Latest logo functionality working\n";
        echo "  - Latest logo: {$latestLogo->filename}\n";
        echo "  - Created at: {$latestLogo->created_at}\n\n";
    } else {
        echo "✗ No logos found\n\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "✗ Error testing latest logo: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 7: Check storage directory
echo "7. Checking storage directory...\n";
try {
    $logoStoragePath = storage_path('app/public/logos');
    
    if (!is_dir($logoStoragePath)) {
        echo "✗ Logos storage directory does not exist: {$logoStoragePath}\n";
        echo "Creating directory...\n";
        mkdir($logoStoragePath, 0755, true);
        echo "✓ Directory created\n\n";
    } else {
        echo "✓ Logos storage directory exists: {$logoStoragePath}\n\n";
    }
    
    // Check if directory is writable
    if (!is_writable($logoStoragePath)) {
        echo "✗ Logos storage directory is not writable\n\n";
        exit(1);
    } else {
        echo "✓ Logos storage directory is writable\n\n";
    }
} catch (Exception $e) {
    echo "✗ Error checking storage directory: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 8: Clean up test data
echo "8. Cleaning up test data...\n";
try {
    $deletedCount = DB::table('school_logos')->where('filename', 'LIKE', 'test_%')->delete();
    echo "✓ Cleaned up {$deletedCount} test records\n\n";
} catch (Exception $e) {
    echo "✗ Error cleaning up: " . $e->getMessage() . "\n\n";
}

echo "=== ALL TESTS COMPLETED SUCCESSFULLY ===\n";
echo "The logo upload system is ready to use!\n\n";

echo "Next steps:\n";
echo "1. Make sure Laravel server is running: php artisan serve\n";
echo "2. Test the API endpoints:\n";
echo "   - GET /api/logos/current\n";
echo "   - GET /api/logos/latest\n";
echo "   - GET /api/logos/all\n";
echo "   - POST /api/logos/upload (requires authentication)\n\n";
