<?php

/**
 * Test script untuk upload gallery ke frontend folder
 * Run: php test_gallery_upload.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use App\Http\Controllers\GalleryController;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== TESTING GALLERY UPLOAD TO FRONTEND ===\n\n";

// Test 1: Check frontend folder structure
echo "1. Checking frontend gallery folder structure...\n";
$frontendPath = base_path('../frontend/public/images/gallery');
echo "Frontend gallery path: {$frontendPath}\n";

if (!is_dir($frontendPath)) {
    echo "❌ Frontend gallery directory does not exist\n";
    echo "Creating directory...\n";
    mkdir($frontendPath, 0755, true);
    echo "✅ Directory created\n";
} else {
    echo "✅ Frontend gallery directory exists\n";
}

if (!is_writable($frontendPath)) {
    echo "❌ Frontend gallery directory is not writable\n";
    exit(1);
} else {
    echo "✅ Frontend gallery directory is writable\n\n";
}

// Test 2: Test upload to frontend folder
echo "2. Testing gallery upload to frontend folder...\n";
try {
    // Create a simple test image content (1x1 pixel PNG)
    $pngData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==');
    
    // Create temporary file
    $tempFile = tempnam(sys_get_temp_dir(), 'test_gallery') . '.png';
    file_put_contents($tempFile, $pngData);
    
    // Create UploadedFile instance
    $uploadedFile = new UploadedFile(
        $tempFile,
        'test_gallery_image.png',
        'image/png',
        null,
        true // test mode
    );
    
    // Create request with file and data
    $request = new Request([
        'title' => 'Test Gallery Image',
        'description' => 'Test gallery upload from backend',
        'category' => 'Kegiatan',
        'featured' => '0',
        'is_active' => '1',
        'sort_order' => '0'
    ]);
    $request->files->set('image', $uploadedFile);
    
    echo "📁 Test file created: {$tempFile}\n";
    echo "📊 File size: " . filesize($tempFile) . " bytes\n";
    
    // Test upload using controller
    $controller = new GalleryController();
    $response = $controller->store($request);
    $responseData = json_decode($response->getContent(), true);
    
    if ($response->getStatusCode() === 201 && $responseData['success']) {
        echo "✅ Upload successful!\n";
        echo "📁 Saved as: " . basename($responseData['data']['image_url']) . "\n";
        echo "🗄️ Database ID: " . $responseData['data']['id'] . "\n";
        echo "🔗 URL: " . $responseData['data']['image_url'] . "\n";
        echo "📂 Title: " . $responseData['data']['title'] . "\n";
        echo "📝 Category: " . $responseData['data']['category'] . "\n\n";
        
        // Verify file exists in frontend folder
        $filename = basename($responseData['data']['image_url']);
        $frontendFilePath = $frontendPath . '/' . $filename;
        
        if (file_exists($frontendFilePath)) {
            echo "✅ File exists in frontend folder: {$frontendFilePath}\n";
            echo "📊 Frontend file size: " . filesize($frontendFilePath) . " bytes\n";
        } else {
            echo "❌ File not found in frontend folder: {$frontendFilePath}\n";
        }
        
        // Verify database record
        $galleryId = $responseData['data']['id'];
        $dbRecord = DB::table('galleries')->where('id', $galleryId)->first();
        if ($dbRecord) {
            echo "✅ Database record exists:\n";
            echo "  - ID: {$dbRecord->id}\n";
            echo "  - Title: {$dbRecord->title}\n";
            echo "  - Description: {$dbRecord->description}\n";
            echo "  - Category: {$dbRecord->category}\n";
            echo "  - Image URL: {$dbRecord->image_url}\n";
            echo "  - Is Active: {$dbRecord->is_active}\n";
            echo "  - Featured: {$dbRecord->featured}\n\n";
        } else {
            echo "❌ Database record not found\n\n";
        }
        
    } else {
        echo "❌ Upload failed\n";
        echo "Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n\n";
    }
    
    // Clean up temp file
    if (file_exists($tempFile)) {
        unlink($tempFile);
        echo "🧹 Temporary file cleaned up\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error in upload test: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n\n";
}

// Test 3: List files in frontend gallery directory
echo "3. Listing files in frontend gallery directory...\n";
try {
    $files = scandir($frontendPath);
    $galleryFiles = array_filter($files, function($file) {
        return !in_array($file, ['.', '..', '.gitkeep']);
    });
    
    echo "📁 Files in frontend gallery directory: " . count($galleryFiles) . "\n";
    foreach ($galleryFiles as $file) {
        $filePath = $frontendPath . '/' . $file;
        $fileSize = filesize($filePath);
        echo "  - {$file} ({$fileSize} bytes)\n";
    }
    echo "\n";
    
} catch (Exception $e) {
    echo "❌ Error listing files: " . $e->getMessage() . "\n\n";
}

// Test 4: Test API endpoints
echo "4. Testing gallery API endpoints...\n";
try {
    $controller = new GalleryController();
    
    // Test adminIndex
    $request = new Request();
    $response = $controller->adminIndex($request);
    $responseData = json_decode($response->getContent(), true);
    $galleryCount = $responseData['success'] ? count($responseData['data']) : 0;
    echo "✅ adminIndex: {$galleryCount} gallery items found\n";
    
    // Test public index
    $response = $controller->index($request);
    $responseData = json_decode($response->getContent(), true);
    $publicGalleryCount = $responseData['success'] ? count($responseData['data']) : 0;
    echo "✅ public index: {$publicGalleryCount} active gallery items found\n\n";
    
} catch (Exception $e) {
    echo "❌ Error testing API endpoints: " . $e->getMessage() . "\n\n";
}

echo "=== TEST COMPLETED ===\n";
echo "Gallery upload system is ready!\n\n";

echo "File structure:\n";
echo "frontend/public/images/gallery/ - Gallery upload folder\n";
echo "frontend/public/images/logo/ - Logo upload folder\n";
echo "frontend/public/images/news/ - News images folder (future)\n\n";

echo "Next steps:\n";
echo "1. Start frontend server: npm run dev (port 5173)\n";
echo "2. Start backend server: php artisan serve (port 8000)\n";
echo "3. Test upload from frontend gallery management\n";
echo "4. Check uploaded files in frontend/public/images/gallery/\n\n";
