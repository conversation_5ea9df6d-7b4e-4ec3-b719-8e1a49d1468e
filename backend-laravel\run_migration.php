<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;
use Illuminate\Database\Schema\Blueprint;

// Database configuration
$capsule = new Capsule;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'school_management',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    echo "Running migration: Add status column to news table...\n";
    
    // Check if status column already exists
    $hasStatusColumn = Capsule::schema()->hasColumn('news', 'status');
    
    if (!$hasStatusColumn) {
        Capsule::schema()->table('news', function (Blueprint $table) {
            // Add status column with enum values
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft')->after('published');
            
            // Add index for better performance
            $table->index('status');
        });
        
        echo "✅ Status column added successfully!\n";
        
        // Update existing records based on published field
        echo "Updating existing records...\n";
        
        $updatedCount = Capsule::table('news')
            ->where('published', true)
            ->update(['status' => 'published']);
            
        echo "✅ Updated {$updatedCount} published records to 'published' status\n";
        
        $draftCount = Capsule::table('news')
            ->where('published', false)
            ->update(['status' => 'draft']);
            
        echo "✅ Updated {$draftCount} unpublished records to 'draft' status\n";
        
    } else {
        echo "⚠️  Status column already exists, skipping migration.\n";
    }
    
    echo "\n🎉 Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
