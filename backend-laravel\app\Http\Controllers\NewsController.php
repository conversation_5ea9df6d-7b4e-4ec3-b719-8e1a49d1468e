<?php

namespace App\Http\Controllers;

use App\Models\News;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Exception;

class NewsController extends Controller
{
    /**
     * Get published news (public)
     */
    public function index(Request $request)
    {
        $query = News::with('author:id,name')
            ->published()
            ->orderBy('published_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Featured filter
        if ($request->has('featured') && $request->featured) {
            $query->featured();
        }

        // Pagination
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        
        $news = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'total' => $news->total(),
            'page' => $news->currentPage(),
            'pages' => $news->lastPage(),
        ]);
    }

    /**
     * Get single news by ID (public)
     */
    public function show($id)
    {
        $news = News::with('author:id,name')
            ->published()
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $news
        ]);
    }

    /**
     * Get all news for admin (including unpublished)
     */
    public function adminIndex(Request $request)
    {
        $query = News::with('author:id,name')
            ->orderBy('created_at', 'desc');

        // Search
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Status filter
        if ($request->has('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->where('published', false);
            }
        }

        // Pagination
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 10);
        
        $news = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => $news->items(),
            'total' => $news->total(),
            'page' => $news->currentPage(),
            'pages' => $news->lastPage(),
        ]);
    }

    /**
     * Create new news (admin only)
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'excerpt' => 'nullable|string',
                'category' => 'required|string|max:255',
                'author' => 'required|string|max:255',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
                'published' => 'boolean',
                'featured' => 'boolean',
                'status' => 'nullable|in:draft,published,archived',
                'tags' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validator->errors()->first()
                ], 400);
            }

        // Determine status and published state (sync logic)
        $status = $request->get('status', null);
        $published = $request->has('published') ? $request->boolean('published') : null;

        // Sync logic: status <-> published
        if ($status === 'published') {
            $published = true;
        } elseif ($status === 'draft' || $status === 'archived') {
            $published = false;
        } elseif ($published !== null) {
            // If published sent directly but status not set, set status accordingly
            $status = $published ? 'published' : 'draft';
        } else {
            // Default
            $status = 'draft';
            $published = false;
        }

        error_log('📝 Status received: ' . $status);
        error_log('📝 Published state: ' . ($published ? 'true' : 'false'));


        // Create news first (image_url null), then handle image if present
        $news = News::create([
            'title' => $request->title,
            'content' => $request->content,
            'excerpt' => $request->excerpt,
            'category' => $request->category,
            'image_url' => null,
            'author_id' => $request->user()->id ?? 1, // Default to user ID 1 if no auth
            'published' => $published,
            'featured' => $request->boolean('featured', false),
            'status' => $status,
            'tags' => $request->tags ?? [],
            'published_at' => $published ? now() : null,
        ]);

        // Handle image upload if present
        if ($request->hasFile('image')) {
            try {
                $image = $request->file('image');
                $imageExt = $image->getClientOriginalExtension();
                $imageName = 'news_' . $news->id . '.' . $imageExt;
                $uploadPath = __DIR__ . '/../../../../frontend/public/images/news';
                
                // Ensure upload directory exists
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                
                // Upload new image
                if ($image->move($uploadPath, $imageName)) {
                    $news->image_url = '/images/news/' . $imageName;
                    $news->save();
                    error_log('✅ Image uploaded successfully: ' . $imageName);
                } else {
                    throw new Exception('Failed to move uploaded image');
                }
            } catch (Exception $e) {
                error_log('❌ Image upload failed: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to upload image: ' . $e->getMessage()
                ], 500);
            }
        } else {
            error_log('ℹ️ No image uploaded with new news');
        }

        // Add author name to the response
        $news->author_name = $request->author;
        $news->load('author:id,name');

        return response()->json([
            'success' => true,
            'message' => 'News created successfully',
            'data' => $news
        ], 201);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to create news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update news (admin only)
     */
    public function update(Request $request, $id)
    {
        try {
            $news = News::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'content' => 'sometimes|required|string',
            'excerpt' => 'nullable|string',
            'category' => 'sometimes|required|string|max:255',
            'author' => 'sometimes|required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'published' => 'boolean',
            'featured' => 'boolean',
            'status' => 'nullable|in:draft,published,archived',
            'tags' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()->first()
            ], 400);
        }


        $updateData = $request->only(['title', 'content', 'excerpt', 'category', 'tags']);
        $uploadPath = __DIR__ . '/../../../../frontend/public/images/news';
        $possibleExts = ['jpg', 'jpeg', 'png', 'gif'];
        $foundImage = null;

        // We'll set image_url after handling upload/delete below

        // Handle image deletion if requested
        $deleteImage = $request->boolean('delete_image', false);
        if ($deleteImage) {
            error_log('�️ Image deletion requested');
            // Delete all possible old images
            foreach ($possibleExts as $ext) {
                $oldFile = $uploadPath . '/news_' . $news->id . '.' . $ext;
                if (file_exists($oldFile)) {
                    unlink($oldFile);
                    error_log('🗑️ Deleted old image: news_' . $news->id . '.' . $ext);
                }
            }
            $updateData['image_url'] = null;
        }
        // Handle new image upload
        else if ($request->hasFile('image')) {
            try {
                $image = $request->file('image');
                $imageExt = $image->getClientOriginalExtension();
                $imageName = 'news_' . $news->id . '.' . $imageExt;
                
                // Ensure upload directory exists
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                
                // Delete old images before uploading new one
                foreach ($possibleExts as $ext) {
                    $oldFile = $uploadPath . '/news_' . $news->id . '.' . $ext;
                    if (file_exists($oldFile)) {
                        unlink($oldFile);
                        error_log('🗑️ Deleted old image before upload: news_' . $news->id . '.' . $ext);
                    }
                }
                
                // Move new image
                if ($image->move($uploadPath, $imageName)) {
                    $updateData['image_url'] = '/images/news/' . $imageName;
                    error_log('✅ Image updated successfully: ' . $imageName);
                } else {
                    throw new Exception('Failed to move uploaded image');
                }
            } catch (Exception $e) {
                error_log('❌ Image update failed: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'error' => 'Failed to update image: ' . $e->getMessage()
                ], 500);
            }
        } else {
            error_log('ℹ️ No image changes requested');
        }

        // Sync status and published logic

        // If no image changes were requested, keep existing image_url
        if (!$request->hasFile('image') && !$deleteImage) {
            unset($updateData['image_url']); // Don't modify existing image_url
        }
        $status = $request->has('status') ? $request->get('status') : null;
        $published = $request->has('published') ? $request->boolean('published') : null;

        if ($status === 'published') {
            $updateData['status'] = 'published';
            $updateData['published'] = true;
            if (!$news->published_at) {
                $updateData['published_at'] = now();
            }
            error_log('✅ Setting published = true (status published)');
        } elseif ($status === 'draft' || $status === 'archived') {
            $updateData['status'] = $status;
            $updateData['published'] = false;
            $updateData['published_at'] = null;
            error_log('❌ Setting published = false (status draft/archived)');
        } elseif ($published !== null) {
            $updateData['published'] = $published;
            $updateData['status'] = $published ? 'published' : 'draft';
            if ($published && !$news->published_at) {
                $updateData['published_at'] = now();
            }
            if (!$published) {
                $updateData['published_at'] = null;
            }
            error_log('🔄 Setting status and published from published field');
        }

        if ($request->has('featured')) {
            $updateData['featured'] = $request->boolean('featured');
        }

        $news->update($updateData);
        $news->load('author:id,name');

        // Add author name to the response if provided
        if ($request->has('author')) {
            $news->author_name = $request->author;
        }

        return response()->json([
            'success' => true,
            'message' => 'News updated successfully',
            'data' => $news
        ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update news: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete news (admin only)
     */
    public function destroy($id)
    {
        try {
            $news = News::findOrFail($id);

            // Delete image file if exists (always use /frontend/public/images/news)
            if ($news->image_url) {
                $uploadPath = __DIR__ . '/../../../../frontend/public/images/news';
                $imagePath = $uploadPath . '/' . $news->image_url;
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                    error_log('🗑️ Deleted image on destroy: ' . $news->image_url);
                }
            }

            $news->delete();

            return response()->json([
                'success' => true,
                'message' => 'News deleted successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to delete news: ' . $e->getMessage()
            ], 500);
        }
    }
}
