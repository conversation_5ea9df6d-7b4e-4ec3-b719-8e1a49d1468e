<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('galleries', function (Blueprint $table) {
            // Add composite indexes for better query performance
            $table->index(['created_at', 'is_active'], 'galleries_created_active_idx');
            $table->index(['category', 'created_at'], 'galleries_category_created_idx');
            $table->index(['featured', 'created_at'], 'galleries_featured_created_idx');
            $table->index(['carousel_pinned', 'created_at'], 'galleries_carousel_created_idx');
            
            // Add fulltext index for search optimization
            $table->fullText(['title', 'description'], 'galleries_search_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('galleries', function (Blueprint $table) {
            $table->dropIndex('galleries_created_active_idx');
            $table->dropIndex('galleries_category_created_idx');
            $table->dropIndex('galleries_featured_created_idx');
            $table->dropIndex('galleries_carousel_created_idx');
            $table->dropIndex('galleries_search_idx');
        });
    }
};
