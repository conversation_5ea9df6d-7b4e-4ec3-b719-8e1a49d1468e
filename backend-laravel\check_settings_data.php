<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as DB;

// Setup database connection
$capsule = new DB;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => 'localhost',
    'database' => 'school_management',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

echo "=== CHECKING SETTINGS DATA ===\n\n";

try {
    // Check current settings
    $settings = DB::table('settings')->get();
    
    echo "1. Current settings in database:\n";
    foreach ($settings as $setting) {
        $value = is_string($setting->value) ? $setting->value : json_encode($setting->value);
        echo "   {$setting->key}: {$value}\n";
    }
    
    echo "\n2. Checking for schoolAddress:\n";
    $addressSetting = DB::table('settings')->where('key', 'schoolAddress')->first();
    
    if ($addressSetting) {
        echo "✅ schoolAddress found: {$addressSetting->value}\n";
    } else {
        echo "❌ schoolAddress not found. Creating it...\n";
        
        // Create schoolAddress setting
        DB::table('settings')->insert([
            'key' => 'schoolAddress',
            'value' => '"Jl. Pendidikan No. 123, Menteng, Jakarta Pusat, DKI Jakarta 10310"',
            'type' => 'string',
            'category' => 'contact',
            'description' => 'Alamat lengkap sekolah',
            'is_public' => true,
            'updated_by' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        
        echo "✅ schoolAddress created successfully!\n";
    }
    
    echo "\n3. Testing API endpoint:\n";
    echo "You can test the settings API at: http://localhost:8000/api/settings\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== SETTINGS CHECK COMPLETED ===\n";
