<?php

/**
 * Script untuk memeriksa dan memperbaiki data kategori gallery
 */

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

echo "=== CHECKING GALLERY DATA ===\n\n";

// Check current data
echo "1. Current gallery data:\n";
$galleries = DB::table('galleries')->select('id', 'title', 'category')->get();

foreach ($galleries as $gallery) {
    echo "ID: {$gallery->id} | Title: {$gallery->title} | Category: {$gallery->category}\n";
}

echo "\n";

// Valid categories according to database enum
$validCategories = ['Fasilitas', 'Kegiatan', 'Prestasi', 'Ekstrakurikuler', 'Umum'];

echo "2. Valid categories: " . implode(', ', $validCategories) . "\n\n";

// Check for invalid categories
echo "3. Checking for invalid categories:\n";
$invalidData = [];

foreach ($galleries as $gallery) {
    if (!in_array($gallery->category, $validCategories)) {
        $invalidData[] = $gallery;
        echo "❌ Invalid category found - ID: {$gallery->id}, Category: '{$gallery->category}'\n";
    }
}

if (empty($invalidData)) {
    echo "✅ All categories are valid!\n\n";
} else {
    echo "\n4. Fixing invalid categories:\n";
    
    // Mapping old categories to new ones
    $categoryMapping = [
        'Kegiatan Rutin' => 'Kegiatan',
        'Kegiatan Khusus' => 'Kegiatan',
        'Prestasi Siswa' => 'Prestasi',
        'Fasilitas Sekolah' => 'Fasilitas',
        'Ekstrakurikuler Sekolah' => 'Ekstrakurikuler',
        'Lainnya' => 'Umum',
        'Other' => 'Umum',
        'General' => 'Umum'
    ];
    
    foreach ($invalidData as $gallery) {
        $newCategory = $categoryMapping[$gallery->category] ?? 'Umum';
        
        try {
            DB::table('galleries')
                ->where('id', $gallery->id)
                ->update(['category' => $newCategory]);
            
            echo "✅ Fixed ID {$gallery->id}: '{$gallery->category}' → '{$newCategory}'\n";
        } catch (Exception $e) {
            echo "❌ Error fixing ID {$gallery->id}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n5. Verification after fix:\n";
    $updatedGalleries = DB::table('galleries')->select('id', 'title', 'category')->get();
    
    foreach ($updatedGalleries as $gallery) {
        $status = in_array($gallery->category, $validCategories) ? '✅' : '❌';
        echo "{$status} ID: {$gallery->id} | Title: {$gallery->title} | Category: {$gallery->category}\n";
    }
}

echo "\n=== GALLERY DATA CHECK COMPLETED ===\n";
