<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('school_logos', function (Blueprint $table) {
            $table->id();
            $table->string('filename')->unique(); // school_logo_randomstring.ext
            $table->string('original_name'); // nama file asli
            $table->string('file_path'); // path lengkap file
            $table->string('mime_type'); // image/jpeg, image/png, etc
            $table->integer('file_size'); // ukuran file dalam bytes
            $table->boolean('is_active')->default(true); // logo yang sedang aktif
            $table->text('description')->nullable(); // deskripsi logo
            $table->timestamps();
            
            // Index untuk performa
            $table->index('is_active');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('school_logos');
    }
};
