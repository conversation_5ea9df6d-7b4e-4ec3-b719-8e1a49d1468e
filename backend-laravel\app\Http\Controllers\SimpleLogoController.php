<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SimpleLogoController extends Controller
{
    /**
     * Upload logo file
     */
    public function upload(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'logo' => 'required|image|mimes:jpeg,jpg,png,webp|max:5120', // 5MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'Validation failed',
                'details' => $validator->errors()
            ], 400);
        }

        try {
            $logoFile = $request->file('logo');
            
            // Generate unique filename: logo_timestamp.ext
            $timestamp = time();
            $extension = $logoFile->getClientOriginalExtension();
            $filename = "logo_{$timestamp}.{$extension}";
            
            // Define logo path in root/uploads/images/logo
            $logoPath = base_path('../../uploads/images/logo');
            
            // Create directory if it doesn't exist
            if (!is_dir($logoPath)) {
                mkdir($logoPath, 0755, true);
            }
            
            // Move file to uploads folder
            $logoFile->move($logoPath, $filename);
            
            // Get file size for display
            $filePath = $logoPath . '/' . $filename;
            $fileSize = filesize($filePath);
            $formattedSize = $this->formatBytes($fileSize);
            
            // Generate URL - akan diakses melalui backend
            $logoUrl = "http://localhost:8000/uploads/images/logo/{$filename}";
            
            // Save filename to settings
            $this->saveLogoToSettings($filename, $logoUrl);
            
            return response()->json([
                'success' => true,
                'message' => 'Logo uploaded successfully',
                'data' => [
                    'filename' => $filename,
                    'original_name' => $logoFile->getClientOriginalName(),
                    'file_path' => $filePath,
                    'file_size' => $fileSize,
                    'formatted_size' => $formattedSize,
                    'mime_type' => $logoFile->getMimeType(),
                    'url' => $logoUrl,
                    'uploaded_at' => date('Y-m-d H:i:s')
                ]
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Upload failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get current active logo
     */
    public function getCurrent()
    {
        try {
            // Get logo from settings
            $logoUrl = $this->getLogoFromSettings();

            if (!$logoUrl) {
                return response()->json([
                    'success' => false,
                    'message' => 'No logo found'
                ], 404);
            }

            // Extract filename from URL
            $filename = basename(parse_url($logoUrl, PHP_URL_PATH));

            // Get file info if exists
            $logoPath = base_path('../../uploads/images/logo/' . $filename);
            $fileExists = file_exists($logoPath);
            $fileSize = $fileExists ? filesize($logoPath) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'url' => $logoUrl,
                    'filename' => $filename,
                    'file_exists' => $fileExists,
                    'file_size' => $fileSize,
                    'formatted_size' => $this->formatBytes($fileSize)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get current logo',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Save logo info to settings
     */
    private function saveLogoToSettings($filename, $url)
    {
        // Update logoUrl in settings
        \App\Models\Setting::updateOrCreate(
            ['key' => 'logoUrl'],
            [
                'value' => $url,
                'category' => 'general',
                'is_public' => true,
                'updated_by' => auth()->id() ?? 1
            ]
        );
    }
    
    /**
     * Get logo from settings
     */
    private function getLogoFromSettings()
    {
        $setting = \App\Models\Setting::where('key', 'logoUrl')->first();
        return $setting ? $setting->value : null;
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
