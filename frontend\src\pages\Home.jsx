import React, { useState, useEffect, useCallback } from 'react';
import { useSchoolSettings } from '../hooks/useSchoolSettings';
import { useCarouselGallery } from '../hooks/useGallery';
import { useNews } from '../hooks/useNews';
import ImageSlider from '../components/ImageSlider';
import ProfileSection from '../components/ProfileSection';
import NewsSection from '../components/NewsSection';
import ImageModal from '../components/ImageModal';

const Home = () => {
  const { settings, loading } = useSchoolSettings();
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const {
    images: carouselGallery,
    loading: galleryLoading,
    refreshCarousel,
  } = useCarouselGallery(refreshTrigger);
  const { news, loading: newsLoading } = useNews({ limit: 9, featured: false });
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Global refresh & auto reload
  useEffect(() => {
    window.refreshCarousel = () => {
      console.log('Refreshing carousel from admin action...');
      refreshCarousel();
      setRefreshTrigger(prev => prev + 1);
    };
  }, [refreshCarousel]);

  const autoRefreshGallery = useCallback(() => {
    if (!galleryLoading) {
      refreshCarousel();
      setRefreshTrigger(prev => prev + 1);
    }
  }, [galleryLoading, refreshCarousel]);

  useEffect(() => {
    const interval = setInterval(autoRefreshGallery, 60000);
    return () => clearInterval(interval);
  }, [autoRefreshGallery]);

  // ✔ Perbaikan handleImageClick
  const handleImageClick = (image, index) => {
    if (image && typeof index === 'number') {
      setSelectedImage(image);
      setSelectedImageIndex(index);
    }
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const renderCarouselContent = () => {
    if (galleryLoading) {
      return (
        <div className="w-full h-[600px] bg-gray-100 rounded-xl animate-pulse flex items-center justify-center">
          <div className="text-center px-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 text-lg font-medium">Memuat galeri carousel...</p>
            <p className="text-sm text-gray-500 mt-2">Mohon tunggu sebentar</p>
            <div className="mt-4 text-xs text-gray-400">
              <span className="inline-flex items-center">
                <svg className="w-4 h-4 mr-1 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Auto-refresh aktif
              </span>
            </div>
          </div>
        </div>
      );
    }

    if (carouselGallery.length === 0) {
      return (
        <div className="w-full h-[600px] bg-gray-100 rounded-xl flex items-center justify-center">
          <div className="text-center px-4">
            <div className="mx-auto mb-4">
              <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                ></path>
              </svg>
            </div>
            <p className="text-gray-600 text-lg font-medium">Tidak ada gambar di galeri carousel</p>
            <p className="text-sm text-gray-500 mt-2">Gambar akan muncul di sini setelah admin menambahkannya</p>
          </div>
        </div>
      );
    }

    return (
      <ImageSlider
        images={carouselGallery}
        autoPlay={true}
        showButtons={true}
        className="rounded-xl overflow-hidden"
        onImageClick={handleImageClick} // ✔ dipanggil di komponen
      />
    );
  };

  return (
    <div className="min-h-screen">
      {/* Hero Carousel Section */}
      <section className="mb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {renderCarouselContent()}
        </div>
      </section>

      {/* Profile Section */}
      <ProfileSection settings={settings} loading={loading} />

      {/* News Section */}
      <NewsSection news={news} settings={settings} loading={newsLoading} />

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          image={selectedImage}
          images={carouselGallery}
          currentIndex={selectedImageIndex}
          onClose={closeModal}
        />
      )}
    </div>
  );
};

export default Home;
