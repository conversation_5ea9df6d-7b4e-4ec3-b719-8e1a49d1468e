<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DatabaseConnectionTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_connect_to_database()
    {
        // Test basic database connection
        $result = DB::select('SELECT 1 as test');
        
        $this->assertEquals(1, $result[0]->test);
    }

    /** @test */
    public function school_logos_table_exists()
    {
        // Check if school_logos table exists
        $tableExists = DB::select("SHOW TABLES LIKE 'school_logos'");
        
        $this->assertNotEmpty($tableExists, 'school_logos table does not exist');
    }

    /** @test */
    public function school_logos_table_has_correct_structure()
    {
        // Get table structure
        $columns = DB::select("DESCRIBE school_logos");
        
        $columnNames = collect($columns)->pluck('Field')->toArray();
        
        $expectedColumns = [
            'id',
            'filename',
            'original_name',
            'file_path',
            'mime_type',
            'file_size',
            'is_active',
            'description',
            'created_at',
            'updated_at'
        ];
        
        foreach ($expectedColumns as $column) {
            $this->assertContains($column, $columnNames, "Column {$column} is missing from school_logos table");
        }
    }

    /** @test */
    public function it_can_insert_and_retrieve_logo_data()
    {
        // Insert test data
        $logoId = DB::table('school_logos')->insertGetId([
            'filename' => 'test_logo.jpg',
            'original_name' => 'test_logo.jpg',
            'file_path' => 'logos/test_logo.jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1024,
            'is_active' => 1,
            'description' => 'Test logo',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $this->assertIsNumeric($logoId);
        $this->assertGreaterThan(0, $logoId);

        // Retrieve data
        $logo = DB::table('school_logos')->where('id', $logoId)->first();

        $this->assertNotNull($logo);
        $this->assertEquals('test_logo.jpg', $logo->filename);
        $this->assertEquals('test_logo.jpg', $logo->original_name);
        $this->assertEquals('logos/test_logo.jpg', $logo->file_path);
        $this->assertEquals('image/jpeg', $logo->mime_type);
        $this->assertEquals(1024, $logo->file_size);
        $this->assertEquals(1, $logo->is_active);
        $this->assertEquals('Test logo', $logo->description);
    }

    /** @test */
    public function it_can_update_logo_active_status()
    {
        // Insert multiple logos
        $logo1Id = DB::table('school_logos')->insertGetId([
            'filename' => 'logo1.jpg',
            'original_name' => 'logo1.jpg',
            'file_path' => 'logos/logo1.jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 1024,
            'is_active' => 1,
            'description' => 'Logo 1',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $logo2Id = DB::table('school_logos')->insertGetId([
            'filename' => 'logo2.jpg',
            'original_name' => 'logo2.jpg',
            'file_path' => 'logos/logo2.jpg',
            'mime_type' => 'image/jpeg',
            'file_size' => 2048,
            'is_active' => 0,
            'description' => 'Logo 2',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Deactivate all logos
        DB::table('school_logos')->update(['is_active' => 0]);

        // Activate logo 2
        DB::table('school_logos')->where('id', $logo2Id)->update(['is_active' => 1]);

        // Check results
        $logo1 = DB::table('school_logos')->where('id', $logo1Id)->first();
        $logo2 = DB::table('school_logos')->where('id', $logo2Id)->first();

        $this->assertEquals(0, $logo1->is_active);
        $this->assertEquals(1, $logo2->is_active);
    }

    /** @test */
    public function it_can_get_active_logo()
    {
        // Insert logos with different active status
        DB::table('school_logos')->insert([
            [
                'filename' => 'inactive_logo.jpg',
                'original_name' => 'inactive_logo.jpg',
                'file_path' => 'logos/inactive_logo.jpg',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024,
                'is_active' => 0,
                'description' => 'Inactive logo',
                'created_at' => now()->subHour(),
                'updated_at' => now()->subHour()
            ],
            [
                'filename' => 'active_logo.jpg',
                'original_name' => 'active_logo.jpg',
                'file_path' => 'logos/active_logo.jpg',
                'mime_type' => 'image/jpeg',
                'file_size' => 2048,
                'is_active' => 1,
                'description' => 'Active logo',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);

        // Get active logo
        $activeLogo = DB::table('school_logos')
                       ->where('is_active', 1)
                       ->orderBy('created_at', 'desc')
                       ->first();

        $this->assertNotNull($activeLogo);
        $this->assertEquals('active_logo.jpg', $activeLogo->filename);
        $this->assertEquals(1, $activeLogo->is_active);
    }

    /** @test */
    public function it_can_get_latest_logo()
    {
        // Insert logos with different timestamps
        DB::table('school_logos')->insert([
            [
                'filename' => 'old_logo.jpg',
                'original_name' => 'old_logo.jpg',
                'file_path' => 'logos/old_logo.jpg',
                'mime_type' => 'image/jpeg',
                'file_size' => 1024,
                'is_active' => 0,
                'description' => 'Old logo',
                'created_at' => now()->subDay(),
                'updated_at' => now()->subDay()
            ],
            [
                'filename' => 'latest_logo.jpg',
                'original_name' => 'latest_logo.jpg',
                'file_path' => 'logos/latest_logo.jpg',
                'mime_type' => 'image/jpeg',
                'file_size' => 2048,
                'is_active' => 1,
                'description' => 'Latest logo',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);

        // Get latest logo
        $latestLogo = DB::table('school_logos')
                       ->orderBy('created_at', 'desc')
                       ->first();

        $this->assertNotNull($latestLogo);
        $this->assertEquals('latest_logo.jpg', $latestLogo->filename);
    }
}
