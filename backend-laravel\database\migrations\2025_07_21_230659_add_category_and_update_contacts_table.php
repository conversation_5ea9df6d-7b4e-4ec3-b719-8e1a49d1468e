<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            // Add category field
            $table->enum('category', ['Pendaftaran', 'Akademik', 'Administrasi', 'Fasilitas', 'Ekstrakurikuler', 'Lainnya'])
                  ->default('Lainnya')
                  ->after('message');

            // Update status enum values
            $table->dropColumn('status');
        });

        // Add status column with new enum values
        Schema::table('contacts', function (Blueprint $table) {
            $table->enum('status', ['Pending', 'Responded', 'Resolved'])
                  ->default('Pending')
                  ->after('category');
        });

        // Update priority enum values
        Schema::table('contacts', function (Blueprint $table) {
            $table->dropColumn('priority');
        });

        Schema::table('contacts', function (Blueprint $table) {
            $table->enum('priority', ['Rendah', 'Sedang', 'Tinggi', 'Urgent'])
                  ->default('Sedang')
                  ->after('status');
        });

        // Add new timestamp fields
        Schema::table('contacts', function (Blueprint $table) {
            $table->timestamp('responded_at')->nullable()->after('replied_at');
            $table->timestamp('resolved_at')->nullable()->after('responded_at');
            $table->text('admin_notes')->nullable()->after('resolved_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            // Remove new fields
            $table->dropColumn(['category', 'responded_at', 'resolved_at', 'admin_notes']);

            // Restore original status enum
            $table->dropColumn('status');
        });

        Schema::table('contacts', function (Blueprint $table) {
            $table->enum('status', ['unread', 'read', 'replied', 'archived'])->default('unread');
        });

        // Restore original priority enum
        Schema::table('contacts', function (Blueprint $table) {
            $table->dropColumn('priority');
        });

        Schema::table('contacts', function (Blueprint $table) {
            $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
        });
    }
};
