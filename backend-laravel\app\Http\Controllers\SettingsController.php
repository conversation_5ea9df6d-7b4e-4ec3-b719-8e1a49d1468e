<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SettingsController extends Controller
{
    /**
     * Get public settings
     */
    public function index()
    {
        $settings = Setting::getPublicSettings();

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Get all settings (admin only)
     */
    public function adminIndex()
    {
        $settings = Setting::with('updatedByUser:id,name')
            ->orderBy('category')
            ->orderBy('key')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Update settings (admin only)
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            '*' => 'required', // All fields are required
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => 'All fields are required'
            ], 400);
        }

        $updatedSettings = [];
        $userId = $request->user()->id;

        foreach ($request->all() as $key => $value) {
            if ($value !== null) {
                $setting = Setting::setValue($key, $value, $userId);
                $updatedSettings[] = $setting;
            }
        }

        $allSettings = Setting::getPublicSettings();

        return response()->json([
            'success' => true,
            'message' => 'Settings updated successfully',
            'data' => $allSettings,
            'updated' => $updatedSettings
        ]);
    }

    /**
     * Get setting by key
     */
    public function show($key)
    {
        $setting = Setting::where('key', $key)->first();

        if (!$setting) {
            return response()->json([
                'success' => false,
                'error' => 'Setting not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $setting
        ]);
    }

    /**
     * Create or update single setting (admin only)
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string|max:255',
            'value' => 'required',
            'type' => 'required|in:string,number,boolean,object,array',
            'description' => 'nullable|string',
            'category' => 'required|in:general,contact,social,appearance,system',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()->first()
            ], 400);
        }

        $setting = Setting::setValue(
            $request->key,
            $request->value,
            $request->user()->id,
            [
                'type' => $request->type,
                'description' => $request->description,
                'category' => $request->category,
                'is_public' => $request->boolean('is_public', true),
            ]
        );

        return response()->json([
            'success' => true,
            'message' => 'Setting saved successfully',
            'data' => $setting
        ]);
    }

    /**
     * Delete setting (admin only)
     */
    public function destroy($key)
    {
        $setting = Setting::where('key', $key)->first();

        if (!$setting) {
            return response()->json([
                'success' => false,
                'error' => 'Setting not found'
            ], 404);
        }

        $setting->delete();

        return response()->json([
            'success' => true,
            'message' => 'Setting deleted successfully'
        ]);
    }

    /**
     * Upload logo file
     */
    public function uploadLogo(Request $request)
    {
        try {
            $request->validate([
                'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048'
            ]);

            if ($request->hasFile('logo')) {
                $file = $request->file('logo');
                $filename = 'school-logo.' . $file->getClientOriginalExtension();

                // Create uploads directory if it doesn't exist
                $uploadPath = public_path('uploads/logo');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // Move file to public/uploads/logo
                $file->move($uploadPath, $filename);

                // Update logoUrl setting
                $logoUrl = '/uploads/logo/' . $filename;

                Setting::updateOrCreate(
                    ['key' => 'logoUrl'],
                    [
                        'value' => json_encode($logoUrl),
                        'type' => 'string',
                        'category' => 'appearance',
                        'description' => 'URL logo sekolah',
                        'is_public' => true,
                        'updated_by' => auth()->id(),
                    ]
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Logo uploaded successfully',
                    'data' => [
                        'logoUrl' => $logoUrl
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No file uploaded'
            ], 400);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading logo: ' . $e->getMessage()
            ], 500);
        }
    }
}
