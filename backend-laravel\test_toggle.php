<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use App\Http\Controllers\UserController;
use Illuminate\Http\Request;

try {
    echo "=== TESTING TOGGLE STATUS ===\n";
    
    // Get users
    $users = User::all();
    echo "Available users:\n";
    foreach ($users as $user) {
        echo "  ID: {$user->id}, Name: {$user->name}, Role: {$user->role}, Active: " . ($user->is_active ? 'Yes' : 'No') . "\n";
    }
    
    // Find a non-admin user to test
    $testUser = User::where('role', '!=', 'admin')->first();
    if (!$testUser) {
        echo "No non-admin user found for testing\n";
        exit;
    }
    
    echo "\nTesting with user: {$testUser->name} (ID: {$testUser->id})\n";
    echo "Current status: " . ($testUser->is_active ? 'Active' : 'Inactive') . "\n";
    
    // Test toggle
    $controller = new UserController();
    $response = $controller->toggleStatus($testUser->id);
    
    echo "Response: " . $response->getContent() . "\n";
    
    // Check updated status
    $testUser->refresh();
    echo "New status: " . ($testUser->is_active ? 'Active' : 'Inactive') . "\n";
    
    echo "\n=== TEST COMPLETED ===\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
