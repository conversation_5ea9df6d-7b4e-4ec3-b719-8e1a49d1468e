<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;
use App\Models\User;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get admin user
        $admin = User::where('role', 'admin')->first();
        
        if (!$admin) {
            echo "❌ Admin user not found. Please run UserSeeder first.\n";
            return;
        }

        echo "🔧 Creating school settings...\n";

        // Create school settings
        $schoolSettings = [
            ['key' => 'schoolName', 'value' => 'SMA Negeri 1 Jakarta', 'type' => 'string', 'category' => 'general', 'description' => 'Nama lengkap sekolah'],
            ['key' => 'schoolShortName', 'value' => 'SMAN 1 Jakarta', 'type' => 'string', 'category' => 'general', 'description' => 'Nama singkat sekolah'],
            ['key' => 'schoolEmail', 'value' => '<EMAIL>', 'type' => 'string', 'category' => 'contact', 'description' => 'Email resmi sekolah'],
            ['key' => 'schoolPhone', 'value' => '021-12345678', 'type' => 'string', 'category' => 'contact', 'description' => 'Nomor telepon sekolah'],
            ['key' => 'schoolAddress', 'value' => 'Jl. Pendidikan No. 123, Menteng, Jakarta Pusat, DKI Jakarta 10310', 'type' => 'string', 'category' => 'contact', 'description' => 'Alamat lengkap sekolah'],
            ['key' => 'schoolWebsite', 'value' => 'https://www.sman1jakarta.sch.id', 'type' => 'string', 'category' => 'contact', 'description' => 'Website resmi sekolah'],
            ['key' => 'principalName', 'value' => 'Dr. Ahmad Suryadi, M.Pd', 'type' => 'string', 'category' => 'general', 'description' => 'Nama kepala sekolah'],
            ['key' => 'schoolMotto', 'value' => 'Unggul dalam Prestasi, Berkarakter, dan Berwawasan Global', 'type' => 'string', 'category' => 'general', 'description' => 'Motto sekolah'],
            ['key' => 'schoolDescription', 'value' => 'SMA Negeri 1 Jakarta adalah sekolah menengah atas negeri yang berkomitmen untuk memberikan pendidikan berkualitas tinggi dengan mengembangkan potensi akademik dan karakter siswa.', 'type' => 'string', 'category' => 'general', 'description' => 'Deskripsi sekolah'],
            ['key' => 'logoUrl', 'value' => '/images/logo-school.png', 'type' => 'string', 'category' => 'appearance', 'description' => 'URL logo sekolah']
        ];

        foreach ($schoolSettings as $settingData) {
            Setting::updateOrCreate(
                ['key' => $settingData['key']],
                [
                    'value' => $settingData['value'],
                    'type' => $settingData['type'],
                    'category' => $settingData['category'],
                    'description' => $settingData['description'],
                    'is_public' => true,
                    'updated_by' => $admin->id,
                ]
            );
            echo "  ✅ {$settingData['key']}: {$settingData['value']}\n";
        }

        echo "✅ School settings created successfully!\n";
    }
}
